package notification

import (
	"context"
	"os"

	firebase "firebase.google.com/go/v4"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository/notification"
	"github.com/dsoplabs/dinbora-backend/internal/repository/user"
	_firebase "github.com/dsoplabs/dinbora-backend/internal/service/firebase"
)

// EmailNotifier defines the interface for sending email notifications across different providers
type EmailNotifier interface {
	// SendPasswordReset sends a password reset email to the recipient
	SendPasswordReset(ctx context.Context, recipientEmail, recipientName, resetLink string) error

	// SendSubscriptionConfirmation sends a subscription confirmation email
	SendSubscriptionConfirmation(ctx context.Context, recipientEmail, recipientName, planType string) error

	// SendSubscriptionCancellation sends a subscription cancellation email
	SendSubscriptionCancellation(ctx context.Context, recipientEmail, recipientName, planType string) error

	// SendNewUserWelcome sends a welcome email to new users with password setup link
	SendNewUserWelcome(ctx context.Context, recipientEmail, recipientName, resetLink, planType string) error

	// SendGenericEmail sends a generic email with custom content
	SendGenericEmail(ctx context.Context, recipientEmail, recipientName, subject, content string) error

	// AddUserToList adds a user to a Brevo list
	AddUserToList(ctx context.Context, user *model.User, listID int64)

	// UpdateUserInBrevo updates a user in Brevo
	UpdateUserInBrevo(ctx context.Context, oldEmail string, user *model.User)

	// UpdateUserAttributes updates a user's attributes in Brevo
	UpdateUserAttributes(ctx context.Context, userEmail string, attributes map[string]interface{}) error

	// UpdateDinboraPlusStatus updates a user's Dinbora Plus status in Brevo
	UpdateDinboraPlusStatus(ctx context.Context, userEmail string, hasDinboraPlus bool) error
}

// PushNotifier defines the interface for sending push notifications
type PushNotifier interface {
	SendPushNotification(ctx context.Context, userID, title, body string, data map[string]string) error
	ProcessScheduledNotifications(ctx context.Context, notificationType string) error

	// Activity tracking methods
	UpdateLastLogin(ctx context.Context, userID string) error
	UpdateLastTransactionLogged(ctx context.Context, userID string) error
	UpdateLastProgressionActivity(ctx context.Context, userID string) error
	UpdateOnboardingEvent(ctx context.Context, userID, eventType string) error
}

// Service provides direct access to notification providers
//
// Example usage:
//
//	// Send specifically with Gmail
//	if s.GmailNotifier != nil {
//	    err := s.GmailNotifier.SendPasswordReset(ctx, email, name, link)
//	}
//
//	// Send specifically with Brevo
//	if s.BrevoNotifier != nil {
//	    err := s.BrevoNotifier.SendPasswordReset(ctx, email, name, link)
//	}
type Service struct {
	// Direct provider access
	BrevoNotifier EmailNotifier
	GmailNotifier EmailNotifier

	// Push notification provider
	PushNotifier PushNotifier
}

// New creates a new notification service that initializes Brevo, Gmail, and Push notification providers
func New(
	firebaseApp *firebase.App,
	firebaseService _firebase.Service,
	activityStateRepo notification.Repository,
	userRepo user.Repository,
) *Service {
	// Load configuration from environment variables
	config := loadConfigFromEnv()

	var brevoNotifier, gmailNotifier EmailNotifier
	var pushNotifier PushNotifier

	// Initialize Brevo if configured
	if config.BrevoAPIKey != "" {
		if notifier, err := NewBrevoNotifier(config); err == nil {
			brevoNotifier = notifier
		}
	}

	// Initialize Gmail if configured
	if config.GmailClientID != "" && config.GmailClientSecret != "" && config.GmailRefreshToken != "" {
		if notifier, err := NewGmailNotifier(config); err == nil {
			gmailNotifier = notifier
		}
	}

	// Initialize Push notifier if Firebase is configured
	if firebaseApp != nil && firebaseService != nil && activityStateRepo != nil && userRepo != nil {
		pushNotifier = NewFirebasePushNotifier(firebaseApp, firebaseService, activityStateRepo, userRepo)
	}

	return &Service{
		BrevoNotifier: brevoNotifier,
		GmailNotifier: gmailNotifier,
		PushNotifier:  pushNotifier,
	}
}

// Config holds the configuration for notification providers
type Config struct {
	// Brevo configuration
	BrevoAPIKey      string
	BrevoSenderEmail string
	BrevoSenderName  string

	// Gmail configuration
	GmailClientID     string
	GmailClientSecret string
	GmailRefreshToken string
	GmailSenderEmail  string
	GmailSenderName   string

	// Common configuration
	ApiURL string
}

// loadConfigFromEnv loads notification configuration from environment variables
func loadConfigFromEnv() *Config {
	return &Config{
		// Brevo
		BrevoAPIKey:      os.Getenv("BREVO_API_KEY"),
		BrevoSenderEmail: getEnvOrDefault("BREVO_SENDER_EMAIL", "<EMAIL>"),
		BrevoSenderName:  getEnvOrDefault("BREVO_SENDER_NAME", "Suporte Dinbora"),

		// Gmail
		GmailClientID:     os.Getenv("GMAIL_CLIENT_ID"),
		GmailClientSecret: os.Getenv("GMAIL_CLIENT_SECRET"),
		GmailRefreshToken: os.Getenv("GMAIL_REFRESH_TOKEN"),
		GmailSenderEmail:  getEnvOrDefault("GMAIL_SENDER_EMAIL", "<EMAIL>"),
		GmailSenderName:   getEnvOrDefault("GMAIL_SENDER_NAME", "Suporte Dinbora"),

		// Common
		ApiURL: getEnvOrDefault("API_URL", "http://localhost:8080"),
	}
}

// getEnvOrDefault returns the environment variable value or a default value if not set
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
