package middlewares

import (
	"github.com/dsoplabs/dinbora-backend/internal/service/notification"
	"github.com/labstack/echo/v4"
)

const NotificationServiceKey = "notificationService" // Use a constant for the key

// ServiceInjector creates a middleware that injects services into the request context.
func ServiceInjector(notificationService *notification.Service) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Set the service instance on the context for later handlers to use.
			c.Set(NotificationServiceKey, notificationService)
			return next(c)
		}
	}
}
