package errors

// Translation keys for error messages.
//
// These constants map to keys defined in the translation files (locales/*.json).
// Keys are organized by package in alphabetical order (e.g., auth, financialSheet, financialDna, user, etc.).
//
// Key format:
//   Key<Package>Error<SpecificError> = "<package>.error.<specificError>"
//
// Examples:
//   - User not found (from the user package):
//       KeyUserErrorNotFound = "user.error.notFound"
//   - User not found by ID (from the user package):
//       KeyUserErrorNotFoundById = "user.error.notFoundById"

const (
	// AI Assistant

	// AI Context

	// Apple
	KeyAppleErrorAccessTokenRequired                = "apple.error.accessTokenRequired"
	KeyAppleErrorInvalidOboardingAgeRange           = "apple.error.invalidOboardingAgeRange"
	KeyAppleErrorInvalidOboardingFinancialSituation = "apple.error.invalidOboardingFinancialSituation"
	KeyAppleErrorInvalidOboardingFinancialGoal      = "apple.error.invalidOboardingFinancialGoal"
	KeyAppleErrorInvalidOboardingPersonalInterest   = "apple.error.invalidOboardingPersonalInterest"
	KeyAppleErrorFailedToProcessPhoto               = "apple.error.failedToProcessPhoto"
	KeyAppleErrorInvalidFileType                    = "apple.error.invalidFileType"
	KeyAppleErrorFileTooLarge                       = "apple.error.fileTooLarge"
	KeyAppleErrorInvalidLoginInput                  = "apple.error.invalidLoginInput"

	KeyAppleErrorFetchFailed          = "apple.error.fetchFailed"
	KeyAppleErrorDecodeFailed         = "apple.error.decodeFailed"
	KeyAppleErrorInvalidToken         = "apple.error.invalidToken"
	KeyAppleErrorMissingKidToken      = "apple.error.missingKid"
	KeyAppleErrorPublicKeyParseFailed = "apple.error.publicKeyParseFailed"

	// Auth
	KeyAuthErrorInvalidOrExpiredJWT = "auth.error.invalidOrExpiredJWT"

	KeyAuthErrorInvalidLoginInput                  = "auth.error.invalidLoginInput"
	KeyAuthErrorLoginValidationFailed              = "auth.error.loginValidationFailed"
	KeyAuthErrorInvalidRegisterInput               = "auth.error.invalidRegisterInput"
	KeyAuthErrorFailedToParseFormData              = "auth.error.failedToParseFormData"
	KeyAuthErrorInvalidOboardingAgeRange           = "auth.error.invalidOboardingAgeRange"
	KeyAuthErrorInvalidOboardingFinancialSituation = "auth.error.invalidOboardingFinancialSituation"
	KeyAuthErrorInvalidOboardingFinancialGoal      = "auth.error.invalidOboardingFinancialGoal"
	KeyAuthErrorInvalidOboardingPersonalInterest   = "auth.error.invalidOboardingPersonalInterest"
	KeyAuthErrorFailedToProcessPhoto               = "auth.error.failedToProcessPhoto"
	KeyAuthErrorInvalidFileType                    = "auth.error.invalidFileType"
	KeyAuthErrorFileTooLarge                       = "auth.error.fileTooLarge"
	KeyAuthErrorInvalidRefreshTokenInput           = "auth.error.invalidRefreshTokenInput"
	KeyAuthErrorInvalidForgotPasswordInput         = "auth.error.invalidForgotPasswordInput"
	KeyAuthErrorInvalidResetPasswordInput          = "auth.error.invalidResetPasswordInput"
	KeyAuthErrorInvalidCheckPasswordInput          = "auth.error.invalidCheckPasswordInput"
	KeyAuthErrorUserNotLoggedIn                    = "auth.error.userNotLoggedIn"
	KeyAuthErrorInvalidAdminLoginInput             = "auth.error.invalidAdminLoginInput"
	KeyAuthErrorInvalidHRLoginInput                = "auth.error.invalidHRLoginInput"

	KeyAuthErrorFailedToUploadPhoto               = "auth.error.failedToUploadPhoto"
	KeyAuthErrorFailedToCreateToken               = "auth.error.failedToCreateToken"
	KeyAuthErrorFailedToRetrieveUserAfterCreation = "auth.error.failedToRetrieveUserAfterCreation"
	KeyAuthErrorBrevoNotifierNotAvailable         = "auth.error.brevoNotifierNotAvailable"
	KeyAuthErrorUserNotAdmin                      = "auth.error.userNotAdmin"
	KeyAuthErrorUserNotHR                         = "auth.error.userNotHR"

	// Session and OTP
	KeyAuthErrorPhoneRequired         = "auth.error.phoneRequired"
	KeyAuthErrorUserIDRequired        = "auth.error.userIdRequired"
	KeyAuthErrorTokenRequired         = "auth.error.tokenRequired"
	KeyAuthErrorExpirationRequired    = "auth.error.expirationRequired"
	KeyAuthErrorOTPCodeRequired       = "auth.error.otpCodeRequired"
	KeyAuthErrorSessionNotFound       = "auth.error.sessionNotFound"
	KeyAuthErrorOTPNotFound           = "auth.error.otpNotFound"
	KeyAuthErrorSessionExpired        = "auth.error.sessionExpired"
	KeyAuthErrorOTPExpired            = "auth.error.otpExpired"
	KeyAuthErrorOTPGenerationFailed   = "auth.error.otpGenerationFailed"
	KeyAuthErrorInvalidPhoneFormat    = "auth.error.invalidPhoneFormat"
	KeyAuthErrorInvalidOTPCode        = "auth.error.invalidOtpCode"
	KeyAuthErrorOTPVerificationFailed = "auth.error.otpVerificationFailed"
	KeyAuthErrorNotFoundByPhone       = "auth.error.notFoundByPhone"

	// Billing

	// Content

	// Dashboard

	// Dreamboard
	// Repository layer errors
	KeyDreamboardErrorConflict           = "dreamboard.error.conflict"
	KeyDreamboardErrorCreateFailed       = "dreamboard.error.createFailed"
	KeyDreamboardErrorFindFailed         = "dreamboard.error.findFailed"
	KeyDreamboardErrorNotFound           = "dreamboard.error.notFound"
	KeyDreamboardErrorInvalidId          = "dreamboard.error.invalidId"
	KeyDreamboardErrorFindAllFailed      = "dreamboard.error.findAllFailed"
	KeyDreamboardErrorDecodeFailed       = "dreamboard.error.decodeFailed"
	KeyDreamboardErrorFindByUserFailed   = "dreamboard.error.findByUserFailed"
	KeyDreamboardErrorConflictUpdate     = "dreamboard.error.conflictUpdate"
	KeyDreamboardErrorUpdateFailed       = "dreamboard.error.updateFailed"
	KeyDreamboardErrorDeleteFailed       = "dreamboard.error.deleteFailed"
	KeyDreamboardErrorDeleteCreateFailed = "dreamboard.error.deleteCreateFailed"
	KeyDreamboardErrorDeletedNotFound    = "dreamboard.error.deletedNotFound"
	KeyDreamboardErrorDeletedFindFailed  = "dreamboard.error.deletedFindFailed"
	// Dream management errors
	KeyDreamboardErrorDreamAddFailed    = "dreamboard.error.dreamAddFailed"
	KeyDreamboardErrorDreamUpdateFailed = "dreamboard.error.dreamUpdateFailed"
	KeyDreamboardErrorDreamNotFound     = "dreamboard.error.dreamNotFound"
	KeyDreamboardErrorDreamRemoveFailed = "dreamboard.error.dreamRemoveFailed"
	// Category management errors
	KeyDreamboardErrorCategoryAddFailed    = "dreamboard.error.categoryAddFailed"
	KeyDreamboardErrorCategoryNotFound     = "dreamboard.error.categoryNotFound"
	KeyDreamboardErrorCategoryFindFailed   = "dreamboard.error.categoryFindFailed"
	KeyDreamboardErrorCategoryUpdateFailed = "dreamboard.error.categoryUpdateFailed"
	KeyDreamboardErrorCategoryDeleteFailed = "dreamboard.error.categoryDeleteFailed"
	KeyDreamboardErrorCategoryCreateFailed = "dreamboard.error.categoryCreateFailed"
	KeyDreamboardErrorSessionStartFailed   = "dreamboard.error.sessionStartFailed"
	KeyDreamboardErrorTransactionFailed    = "dreamboard.error.transactionFailed"
	// Share link management errors
	KeyDreamboardErrorShareLinkConflict     = "dreamboard.error.shareLinkConflict"
	KeyDreamboardErrorShareLinkCreateFailed = "dreamboard.error.shareLinkCreateFailed"
	KeyDreamboardErrorShareLinkNotFound     = "dreamboard.error.shareLinkNotFound"
	KeyDreamboardErrorShareLinkFindFailed   = "dreamboard.error.shareLinkFindFailed"
	KeyDreamboardErrorShareLinkUpdateFailed = "dreamboard.error.shareLinkUpdateFailed"
	KeyDreamboardErrorShareLinkDeleteFailed = "dreamboard.error.shareLinkDeleteFailed"
	// Contribution management errors
	KeyDreamboardErrorContributionCreateFailed       = "dreamboard.error.contributionCreateFailed"
	KeyDreamboardErrorContributionNotFound           = "dreamboard.error.contributionNotFound"
	KeyDreamboardErrorContributionFindFailed         = "dreamboard.error.contributionFindFailed"
	KeyDreamboardErrorContributionDecodeFailed       = "dreamboard.error.contributionDecodeFailed"
	KeyDreamboardErrorContributionCursorError        = "dreamboard.error.contributionCursorError"
	KeyDreamboardErrorContributionUpdateFailed       = "dreamboard.error.contributionUpdateFailed"
	KeyDreamboardErrorContributionStatusUpdateFailed = "dreamboard.error.contributionStatusUpdateFailed"
	KeyDreamboardErrorContributionDeleteFailed       = "dreamboard.error.contributionDeleteFailed"
	// Model layer validation errors
	KeyDreamboardErrorCategoryIdentifierEmpty     = "dreamboard.error.categoryIdentifierEmpty"
	KeyDreamboardErrorCategoryNameEmpty           = "dreamboard.error.categoryNameEmpty"
	KeyDreamboardErrorCategoryIconEmpty           = "dreamboard.error.categoryIconEmpty"
	KeyDreamboardErrorCategoryColorEmpty          = "dreamboard.error.categoryColorEmpty"
	KeyDreamboardErrorCategoryIdentifierInvalid   = "dreamboard.error.categoryIdentifierInvalid"
	KeyDreamboardErrorCategoryColorInvalid        = "dreamboard.error.categoryColorInvalid"
	KeyDreamboardErrorCategoryUnmarshalFailed     = "dreamboard.error.categoryUnmarshalFailed"
	KeyDreamboardErrorDreamCategoryInvalid        = "dreamboard.error.dreamCategoryInvalid"
	KeyDreamboardErrorDreamTitleInvalid           = "dreamboard.error.dreamTitleInvalid"
	KeyDreamboardErrorDreamTimeFrameInvalid       = "dreamboard.error.dreamTimeFrameInvalid"
	KeyDreamboardErrorDreamDeadlineInvalid        = "dreamboard.error.dreamDeadlineInvalid"
	KeyDreamboardErrorDreamCostNegative           = "dreamboard.error.dreamCostNegative"
	KeyDreamboardErrorDreamSavingsInvalid         = "dreamboard.error.dreamSavingsInvalid"
	KeyDreamboardErrorDreamMoneySourceInvalid     = "dreamboard.error.dreamMoneySourceInvalid"
	KeyDreamboardErrorDreamCreatorRequired        = "dreamboard.error.dreamCreatorRequired"
	KeyDreamboardErrorDreamFundingStatusInvalid   = "dreamboard.error.dreamFundingStatusInvalid"
	KeyDreamboardErrorDreamRaisedAmountNegative   = "dreamboard.error.dreamRaisedAmountNegative"
	KeyDreamboardErrorDreamboardUserRequired      = "dreamboard.error.dreamboardUserRequired"
	KeyDreamboardErrorDreamboardDreamsInvalid     = "dreamboard.error.dreamboardDreamsInvalid"
	KeyDreamboardErrorDreamboardCategoriesInvalid = "dreamboard.error.dreamboardCategoriesInvalid"
	KeyDreamboardErrorDreamboardDatesInvalid      = "dreamboard.error.dreamboardDatesInvalid"
	KeyDreamboardErrorContributionDreamIdRequired = "dreamboard.error.contributionDreamIdRequired"
	KeyDreamboardErrorContributionUserIdRequired  = "dreamboard.error.contributionUserIdRequired"
	KeyDreamboardErrorContributionAmountNegative  = "dreamboard.error.contributionAmountNegative"
	KeyDreamboardErrorContributionStatusInvalid   = "dreamboard.error.contributionStatusInvalid"
	KeyDreamboardErrorShareLinkDreamIdRequired    = "dreamboard.error.shareLinkDreamIdRequired"
	KeyDreamboardErrorShareLinkCodeRequired       = "dreamboard.error.shareLinkCodeRequired"
	KeyDreamboardErrorShareLinkExpired            = "dreamboard.error.shareLinkExpired"
	// Service layer errors
	KeyDreamboardErrorInvalidInput            = "dreamboard.error.invalidInput"
	KeyDreamboardErrorCategoryExists          = "dreamboard.error.categoryExists"
	KeyDreamboardErrorCategoryInUse           = "dreamboard.error.categoryInUse"
	KeyDreamboardErrorCategoryNotInDreamboard = "dreamboard.error.categoryNotInDreamboard"
	KeyDreamboardErrorAlreadyExists           = "dreamboard.error.alreadyExists"
	KeyDreamboardErrorTokenGenerationFailed   = "dreamboard.error.tokenGenerationFailed"
	KeyDreamboardErrorInviteLinkDisabled      = "dreamboard.error.inviteLinkDisabled"
	KeyDreamboardErrorInviteLinkExpired       = "dreamboard.error.inviteLinkExpired"
	KeyDreamboardErrorUserAlreadyContributing = "dreamboard.error.userAlreadyContributing"
	// Controller layer errors
	KeyDreamboardErrorMissingParam           = "dreamboard.error.missingParam"
	KeyDreamboardErrorInvalidType            = "dreamboard.error.invalidType"
	KeyDreamboardErrorDreamIdRequired        = "dreamboard.error.dreamIdRequired"
	KeyDreamboardErrorContributionIdRequired = "dreamboard.error.contributionIdRequired"
	KeyDreamboardErrorCodeRequired           = "dreamboard.error.codeRequired"
	KeyDreamboardErrorUnauthorized           = "dreamboard.error.unauthorized"
	KeyDreamboardErrorAccessDenied           = "dreamboard.error.accessDenied"
	KeyDreamboardErrorValidationRequired     = "dreamboard.error.validationRequired"

	// Financial DNA

	// Financial Plan
	KeyFinancialPlanErrorUnauthorized        = "financialplan.error.unauthorized"
	KeyFinancialPlanErrorInvalidPeriod       = "financialplan.error.invalidPeriod"
	KeyFinancialPlanErrorInvalidCategoryType = "financialplan.error.invalidCategoryType"

	// Financial Sheet
	// Model layer errors
	KeyFinancialSheetErrorInvalidMoneySource        = "financialsheet.error.invalidMoneySource"
	KeyFinancialSheetErrorInvalidPaymentMethod      = "financialsheet.error.invalidPaymentMethod"
	KeyFinancialSheetErrorInvalidCategoryIdentifier = "financialsheet.error.invalidCategoryIdentifier"
	KeyFinancialSheetErrorInvalidCategoryType       = "financialsheet.error.invalidCategoryType"
	KeyFinancialSheetErrorInvalidCategoryBackground = "financialsheet.error.invalidCategoryBackground"
	// Service layer errors
	KeyFinancialSheetErrorInvalidMonth                    = "financialsheet.error.invalidMonth"
	KeyFinancialSheetErrorDuplicateMonth                  = "financialsheet.error.duplicateMonth"
	KeyFinancialSheetErrorSameMonthAsOriginal             = "financialsheet.error.sameMonthAsOriginal"
	KeyFinancialSheetErrorRecordAlreadyExists             = "financialsheet.error.recordAlreadyExists"
	KeyFinancialSheetErrorInvalidRecord                   = "financialsheet.error.invalidRecord"
	KeyFinancialSheetErrorNoTransactionsAlreadyMarked     = "financialsheet.error.noTransactionsAlreadyMarked"
	KeyFinancialSheetErrorNoTransactionsAlreadyMarkedDate = "financialsheet.error.noTransactionsAlreadyMarkedDate"
	KeyFinancialSheetErrorCannotMarkSameDayAsTransaction  = "financialsheet.error.cannotMarkSameDayAsTransaction"
	KeyFinancialSheetErrorInvalidFinancialRecordId        = "financialsheet.error.invalidFinancialRecordId"
	KeyFinancialSheetErrorInvalidCategoryId               = "financialsheet.error.invalidCategoryId"
	KeyFinancialSheetErrorCannotDeleteSystemCategories    = "financialsheet.error.cannotDeleteSystemCategories"
	KeyFinancialSheetErrorCanOnlyDeleteOwnCategories      = "financialsheet.error.canOnlyDeleteOwnCategories"
	KeyFinancialSheetErrorCategoryInUse                   = "financialsheet.error.categoryInUse"
	KeyFinancialSheetErrorTransactionTypeMismatch         = "financialsheet.error.transactionTypeMismatch"
	KeyFinancialSheetErrorInvalidTransaction              = "financialsheet.error.invalidTransaction"
	KeyFinancialSheetErrorInvalidDreamId                  = "financialsheet.error.invalidDreamId"
	KeyFinancialSheetErrorTransactionNotFound             = "financialsheet.error.transactionNotFound"
	KeyFinancialSheetErrorCannotMarkBeforeYesterday       = "financialsheet.error.cannotMarkBeforeYesterday"
	// Controller layer errors
	KeyFinancialSheetErrorInvalidFinancialSheetId    = "financialsheet.error.invalidFinancialSheetId"
	KeyFinancialSheetErrorInvalidMonthParameter      = "financialsheet.error.invalidMonthParameter"
	KeyFinancialSheetErrorInvalidYearParameter       = "financialsheet.error.invalidYearParameter"
	KeyFinancialSheetErrorInvalidInput               = "financialsheet.error.invalidInput"
	KeyFinancialSheetErrorValidationFailed           = "financialsheet.error.validationFailed"
	KeyFinancialSheetErrorDreamTransactionInput      = "financialsheet.error.dreamTransactionInput"
	KeyFinancialSheetErrorDreamTransactionValidation = "financialsheet.error.dreamTransactionValidation"
	KeyFinancialSheetErrorInvalidTransactionId       = "financialsheet.error.invalidTransactionId"
	KeyFinancialSheetErrorInvalidCategoryIdParam     = "financialsheet.error.invalidCategoryIdParam"
	// Repository layer errors
	KeyFinancialSheetErrorConflict                 = "financialsheet.error.conflict"
	KeyFinancialSheetErrorCreateFailed             = "financialsheet.error.createFailed"
	KeyFinancialSheetErrorFindFailed               = "financialsheet.error.findFailed"
	KeyFinancialSheetErrorFindAllFailed            = "financialsheet.error.findAllFailed"
	KeyFinancialSheetErrorNotFound                 = "financialsheet.error.notFound"
	KeyFinancialSheetErrorInvalidId                = "financialsheet.error.invalidId"
	KeyFinancialSheetErrorUserNotFound             = "financialsheet.error.userNotFound"
	KeyFinancialSheetErrorFindByUserFailed         = "financialsheet.error.findByUserFailed"
	KeyFinancialSheetErrorFindByUsersFailed        = "financialsheet.error.findByUsersFailed"
	KeyFinancialSheetErrorConflictUpdate           = "financialsheet.error.conflictUpdate"
	KeyFinancialSheetErrorUpdateFailed             = "financialsheet.error.updateFailed"
	KeyFinancialSheetErrorDeleteFailed             = "financialsheet.error.deleteFailed"
	KeyFinancialSheetCategoryErrorConflict         = "financialsheet.category.error.conflict"
	KeyFinancialSheetCategoryErrorCreateFailed     = "financialsheet.category.error.createFailed"
	KeyFinancialSheetCategoryErrorFindFailed       = "financialsheet.category.error.findFailed"
	KeyFinancialSheetCategoryErrorNotFound         = "financialsheet.category.error.notFound"
	KeyFinancialSheetCategoryErrorInvalidId        = "financialsheet.category.error.invalidId"
	KeyFinancialSheetCategoryErrorConflictUpdate   = "financialsheet.category.error.conflictUpdate"
	KeyFinancialSheetCategoryErrorUpdateFailed     = "financialsheet.category.error.updateFailed"
	KeyFinancialSheetCategoryErrorDeleteFailed     = "financialsheet.category.error.deleteFailed"
	KeyFinancialSheetCategoryErrorFindByIdFailed   = "financialsheet.category.error.findByIdFailed"
	KeyFinancialSheetCategoryErrorFindByNameFailed = "financialsheet.category.error.findByNameFailed"

	// Firebase

	// Gamification

	// Google
	KeyGoogleErrorAccessTokenRequired                = "google.error.accessTokenRequired"
	KeyGoogleErrorInvalidOboardingAgeRange           = "google.error.invalidOboardingAgeRange"
	KeyGoogleErrorInvalidOboardingFinancialSituation = "google.error.invalidOboardingFinancialSituation"
	KeyGoogleErrorInvalidOboardingFinancialGoal      = "google.error.invalidOboardingFinancialGoal"
	KeyGoogleErrorInvalidOboardingPersonalInterest   = "google.error.invalidOboardingPersonalInterest"
	KeyGoogleErrorFailedToProcessPhoto               = "google.error.failedToProcessPhoto"
	KeyGoogleErrorInvalidFileType                    = "google.error.invalidFileType"
	KeyGoogleErrorFileTooLarge                       = "google.error.fileTooLarge"
	KeyGoogleErrorInvalidToken                       = "google.error.invalidToken"

	KeyGoogleErrorFetchFailed  = "google.error.fetchFailed"
	KeyGoogleErrorDecodeFailed = "google.error.decodeFailed"

	// I18n

	// League

	// League Ranking

	// Notification
	KeyNotificationErrorInvalidRequest     = "notification.error.invalidRequest"
	KeyNotificationErrorValidationFailed   = "notification.error.validationFailed"
	KeyNotificationErrorInvalidType        = "notification.error.invalidType"
	KeyNotificationErrorServiceUnavailable = "notification.error.serviceUnavailable"
	KeyNotificationErrorSendFailed         = "notification.error.sendFailed"
	KeyNotificationErrorNotFound           = "notification.error.notFound"

	// Progression - NEW
	KeyProgressionErrorInvalidEvent = "progression.error.invalidEvent"
	// Progression - OLD
	KeyProgressionErrorConflict                    = "progression.error.conflict"
	KeyProgressionErrorCreateFailed                = "progression.error.createFailed"
	KeyProgressionErrorInvalidIdFormat             = "progression.error.invalidIdFormat"
	KeyProgressionErrorNotFound                    = "progression.error.notFound"
	KeyProgressionErrorFindFailed                  = "progression.error.findFailed"
	KeyProgressionErrorNotFoundForUser             = "progression.error.notFoundForUser"
	KeyProgressionErrorFindByUserFailed            = "progression.error.findByUserFailed"
	KeyProgressionErrorUpdateConflict              = "progression.error.updateConflict"
	KeyProgressionErrorUpdateFailed                = "progression.error.updateFailed"
	KeyProgressionErrorNotFoundForUpdate           = "progression.error.notFoundForUpdate"
	KeyProgressionErrorDeleteFailed                = "progression.error.deleteFailed"
	KeyProgressionErrorNotFoundForDeletion         = "progression.error.notFoundForDeletion"
	KeyProgressionErrorFindTrailProgressionsFailed = "progression.error.findTrailProgressionsFailed"

	// RBAC

	// S3

	// User
	KeyUserErrorHashPassword         = "user.error.hashPassword"
	KeyUserErrorForbidden            = "user.error.forbidden"
	KeyUserErrorInvalidCredentials   = "user.error.invalidCredentials"
	KeyUserErrorResetPassword        = "user.error.resetPassword"
	KeyUserErrorMergeFailed          = "user.error.mergeFailed"
	KeyUserErrorProcessPassword      = "user.error.processPassword"
	KeyUserErrorEmailRequired        = "user.error.emailRequired"
	KeyUserErrorInvalidEmail         = "user.error.invalidEmail"
	KeyUserErrorEmptyId              = "user.error.emptyId"
	KeyUserErrorNameRequired         = "user.error.nameRequired"
	KeyUserErrorPasswordRequired     = "user.error.passwordRequired"
	KeyUserErrorReferralCodeRequired = "user.error.referralCodeRequired"
	KeyUserErrorSetRoleNotAllowed    = "user.error.setRoleNotAllowed"
	KeyUserErrorPhoneRequired        = "user.error.phoneRequired"
	KeyUserErrorPasswordRequirements = "user.error.passwordRequirements"
	KeyUserErrorInvalidPhoneNumber   = "user.error.invalidPhoneNumber"

	KeyUserErrorConflict                    = "user.error.conflict"
	KeyUserErrorNotFoundById                = "user.error.notFoundById"
	KeyUserErrorNotFoundByEmail             = "user.error.notFoundByEmail"
	KeyUserErrorNotFoundByPhone             = "user.error.notFoundByPhone"
	KeyUserErrorFindByPhoneFailed           = "user.error.findByPhoneFailed"
	KeyUserErrorNotFoundByReferral          = "user.error.notFoundByReferral"
	KeyUserErrorDeletedNotFoundByEmail      = "user.error.deletedNotFoundByEmail"
	KeyUserErrorConflictUpdate              = "user.error.conflictUpdate"
	KeyUserErrorNotFoundForUpdate           = "user.error.notFoundForUpdate"
	KeyUserErrorNotFoundForDeletion         = "user.error.notFoundForDeletion"
	KeyUserErrorCreateFailed                = "user.error.createFailed"
	KeyUserErrorDeletedCreateFailed         = "user.error.deletedCreateFailed"
	KeyUserErrorFindByIdFailed              = "user.error.findByIdFailed"
	KeyUserErrorFindAllFailed               = "user.error.findAllFailed"
	KeyUserErrorDecodeUserFailed            = "user.error.decodeUserFailed"
	KeyUserErrorAdminUsersNotFound          = "user.error.adminUsersNotFound"
	KeyUserErrorAccessDenied                = "user.error.accessDenied"
	KeyUserErrorDecodeAdminUserFailed       = "user.error.decodeAdminUserFailed"
	KeyUserErrorFindByEmailFailed           = "user.error.findByEmailFailed"
	KeyUserErrorFindByReferralFailed        = "user.error.findByReferralFailed"
	KeyUserErrorFindByReferringUserIdFailed = "user.error.findByReferringUserIdFailed"
	KeyUserErrorCursorError                 = "user.error.cursorError"
	KeyUserErrorFindWithFilterFailed        = "user.error.findWithFilterFailed"
	KeyUserErrorDeletedFindByEmailFailed    = "user.error.deletedFindByEmailFailed"
	KeyUserErrorInvalidId                   = "user.error.invalidId"
	KeyUserErrorUpdateFailed                = "user.error.updateFailed"
	KeyUserErrorDeleteFailed                = "user.error.deleteFailed"
	KeyUserErrorDeletedConflictExists       = "user.error.deletedConflictExists"

	// Vault
)
