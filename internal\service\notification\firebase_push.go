package notification

import (
	"context"
	"log"
	"os"
	"time"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/notification"
	_notification "github.com/dsoplabs/dinbora-backend/internal/repository/notification"
	"github.com/dsoplabs/dinbora-backend/internal/repository/user"
	_firesebase "github.com/dsoplabs/dinbora-backend/internal/service/firebase"
)

// FirebasePushNotifier implements push notifications using Firebase Cloud Messaging
type FirebasePushNotifier struct {
	firebaseApp       *firebase.App
	firebaseService   _firesebase.Service
	activityStateRepo _notification.Repository
	userRepo          user.Repository
}

// NewFirebasePushNotifier creates a new Firebase push notifier
func NewFirebasePushNotifier(
	firebaseApp *firebase.App,
	firebaseService _firesebase.Service,
	activityStateRepo _notification.Repository,
	userRepo user.Repository,
) PushNotifier {
	return &FirebasePushNotifier{
		firebaseApp:       firebaseApp,
		firebaseService:   firebaseService,
		activityStateRepo: activityStateRepo,
		userRepo:          userRepo,
	}
}

// SendPushNotification sends a push notification to a specific user
func (f *FirebasePushNotifier) SendPushNotification(ctx context.Context, userID, title, body string, data map[string]string) error {
	// Get user's FCM token
	fcmToken, err := f.firebaseService.FindByUserID(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to find user FCM token", errors.Internal, err)
	}

	// Get FCM client
	fcmClient, err := f.firebaseApp.Messaging(ctx)
	if err != nil {
		return errors.New(errors.Service, "failed to get Firebase messaging client", errors.Internal, err)
	}

	// Construct message following existing gamification service pattern
	message := &messaging.Message{
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Data: data,

		// Platform-specific configuration (from existing gamification service)
		APNS: &messaging.APNSConfig{
			Headers: map[string]string{
				"apns-priority": "10",
			},
			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					ContentAvailable: true,
					Sound:            "default",
				},
			},
		},

		Android: &messaging.AndroidConfig{
			Priority: "high",
			Notification: &messaging.AndroidNotification{
				ChannelID: "general_notifications",
				Sound:     "default",
			},
		},

		Token: fcmToken.FCMToken,
	}

	// Send message with error handling for unregistered tokens
	messageID, err := fcmClient.Send(ctx, message)
	if err != nil {
		if messaging.IsUnregistered(err) {
			log.Printf("FCM token for user %s is unregistered. Deleting.", userID)
			f.firebaseService.Delete(ctx, userID)
			return nil
		}
		return errors.New(errors.Service, "failed to send push notification", errors.Internal, err)
	}

	log.Printf("Successfully sent push notification to user %s. FCM Message ID: %s", userID, messageID)
	return nil
}

// ProcessScheduledNotifications processes scheduled notifications based on type
func (f *FirebasePushNotifier) ProcessScheduledNotifications(ctx context.Context, notificationType string) error {
	log.Printf("Processing scheduled notifications of type: %s", notificationType)

	appEnv := os.Getenv("API_MODE")

	// --- CHANGE 1: Get the current time ONCE here. We will use this as the reference.
	nowUTC := time.Now().UTC()

	// Get users with FCM tokens
	users, err := f.userRepo.FindUsersWithFCMTokens(ctx)
	if err != nil {
		return errors.New(errors.Service, "failed to find users with FCM tokens", errors.Internal, err)
	}

	if len(users) == 0 {
		log.Println("No users with FCM tokens found")
		return nil
	}

	// Extract user IDs
	userIDs := make([]string, len(users))
	for i, user := range users {
		userIDs[i] = user.ID
	}

	// Get activity states for these users
	activityStates, err := f.activityStateRepo.FindByUserIDs(ctx, userIDs)
	if err != nil {
		return errors.New(errors.Service, "failed to find user activity states", errors.Internal, err)
	}

	// Create a map for quick lookup
	stateMap := make(map[string]*notification.UserActivityState)
	for _, state := range activityStates {
		stateMap[state.UserID] = state
	}

	// Get notification rules for the type
	rules := f.getNotificationRules(notificationType)
	if len(rules) == 0 {
		log.Printf("No notification rules found for type: %s", notificationType)
		return nil
	}

	// Process each user
	processedCount := 0
	for _, user := range users {
		// Get or create activity state for user
		activityState := stateMap[user.ID]
		if activityState == nil {
			// Create default activity state if not found
			activityState = &notification.UserActivityState{
				UserID: user.ID,
			}
			activityState.PrepareCreate()
		}

		// Calculate the start of the day FOR THIS SPECIFIC USER ---
		// This assumes your `user` struct has a `Timezone` field (e.g., "America/New_York")
		if user.Timezone == "" {
			log.Printf("User %s has no timezone set, default.", user.ID)
			user.Timezone = "America/Sao_Paulo"
		}

		location, err := time.LoadLocation(user.Timezone)
		if err != nil {
			log.Printf("Could not load location for user %s (timezone: %s): %v. Skipping.", user.ID, user.Timezone, err)
			continue
		}

		// Convert the universal `nowUTC` into the user's local time to find their "today".
		nowInUserLocation := nowUTC.In(location)
		year, month, day := nowInUserLocation.Date()

		// This is midnight (00:00:00) in the user's specific timezone.
		startOfUserDayInLocalTZ := time.Date(year, month, day, 0, 0, 0, 0, location)

		// Convert the start of their day BACK to UTC for database queries.
		startOfUserDayInUTC := startOfUserDayInLocalTZ.UTC()

		// Find the best notification rule for this user, pass the user-specific start of day to the rule finder
		bestRule := f.findBestNotificationRule(user, activityState, rules, startOfUserDayInUTC)
		if bestRule == nil {
			continue // No eligible notification for this user
		}

		// Send the notification
		data := map[string]string{
			"type":           notificationType,
			"notificationId": bestRule.ID,
		}

		if appEnv == "Production" {
			err := f.SendPushNotification(ctx, user.ID, bestRule.Title, bestRule.Message, data)
			if err != nil {
				log.Printf("Failed to send notification to user %s: %v", user.ID, err)
				continue
			}

			// Record that the notification was sent
			err = f.activityStateRepo.UpdateNotificationSent(ctx, user.ID, bestRule.ID)
			if err != nil {
				log.Printf("Failed to record notification sent for user %s: %v", user.ID, err)
			}
		} else {
			log.Printf("Notification not sent to user %s: %s", user.ID, bestRule.Message)
		}
		processedCount++
	}

	log.Printf("Processed %d notifications of type %s", processedCount, notificationType)
	return nil
}

// Activity tracking methods
const lastLoginThrottleDuration = 1 * time.Hour

// UpdateLastLogin updates the last login timestamp for a user, but only if enough
// time has passed since the last update to avoid excessive database writes.
func (f *FirebasePushNotifier) UpdateLastLogin(ctx context.Context, userID string) error {
	// Step 1: First, try to read the user's current activity state.
	// We need the existing LastLoginAt to make our decision.
	// NOTE: You must ensure your activityStateRepo has this FindByUserID method.
	state, err := f.activityStateRepo.FindByUserID(ctx, userID)

	if err != nil {
		// This could be a real database error, or simply "not found".
		// A "not found" error is OK, it just means this is a new user
		// and we should definitely create their record.
		// If it's a different error, we log it but proceed, letting the
		// final update call handle the potential database issue.
		// Example check for a specific "not found" error:
		if err.(*errors.DomainError).Kind() != errors.NotFound { // You might need to adapt this to your error type
			log.Printf("Warning: could not read activity state for user %s, proceeding with update anyway. Error: %v", userID, err)
		}
		// In either error case (not found or other), we fall through to the update.
		// The update operation uses "upsert", so it will create the record if it doesn't exist.
	}

	// Step 2: If we successfully read the state, apply the throttling logic.
	if state != nil {
		// Check if the last update was recent.
		if time.Since(state.LastLoginAt) < lastLoginThrottleDuration {
			// It was updated recently. Do nothing. This is the successful "throttled" path.
			return nil
		}
	}

	// Step 3: If enough time has passed (or if this is a new user), perform the database write.
	return f.activityStateRepo.UpdateLastLogin(ctx, userID)
}

// UpdateLastTransactionLogged updates the last expense logged timestamp for a user
func (f *FirebasePushNotifier) UpdateLastTransactionLogged(ctx context.Context, userID string) error {
	return f.activityStateRepo.UpdateLastTransactionLogged(ctx, userID)
}

// UpdateLastProgressionActivity updates the last progression activity timestamp for a user
func (f *FirebasePushNotifier) UpdateLastProgressionActivity(ctx context.Context, userID string) error {
	return f.activityStateRepo.UpdateLastProgressionActivity(ctx, userID)
}

// UpdateOnboardingEvent updates onboarding events for a user
func (f *FirebasePushNotifier) UpdateOnboardingEvent(ctx context.Context, userID, eventType string) error {
	return f.activityStateRepo.UpdateOnboardingEvent(ctx, userID, eventType)
}
