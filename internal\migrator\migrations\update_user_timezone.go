package migrations

import (
	"context"
	"fmt"
	"log"

	// model package is not strictly needed here anymore, but good practice
	// "github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type UpdateUserTimezone struct {
	db *mongo.Database
}

func NewUpdateUserTimezone(db *mongo.Database) *UpdateUserTimezone {
	return &UpdateUserTimezone{db: db}
}

func (m *UpdateUserTimezone) Name() string {
	return "update_user_timezone_2025_09_24_fix000"
}

func (m *UpdateUserTimezone) Up(ctx context.Context) error {
	userCollection := m.db.Collection(repository.USERS_COLLECTION)

	// A minimal struct to hold only the fields we need from the DB.
	// This avoids the decoding error because we are not trying to decode the
	// problematic 'timezone' field.
	type UserForMigration struct {
		ObjectID primitive.ObjectID `bson:"_id"`
		Name     string             `bson:"name"` // Good for logging
	}

	// *** THE KEY CHANGE IS HERE ***
	// We build a filter to find users that match ANY of our problem criteria.
	filter := bson.M{
		"$or": []bson.M{
			// Case 1: The timezone was incorrectly stored as an object.
			{"timezone": bson.M{"$type": "object"}},
			// Case 2: The timezone field does not exist.
			{"timezone": bson.M{"$exists": false}},
			// Case 3: The timezone field exists but is null.
			{"timezone": nil},
		},
	}

	cursor, err := userCollection.Find(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to find users with missing or incorrect timezone: %w", err)
	}
	defer cursor.Close(ctx)

	successCount := 0
	errorCount := 0
	const timezoneToSet = "America/Sao_Paulo"

	log.Println("Starting migration for users with missing or incorrect timezones...")

	for cursor.Next(ctx) {
		// Decode into our minimal struct.
		var user UserForMigration
		if err := cursor.Decode(&user); err != nil {
			log.Printf("Error decoding user ID: %v", err)
			errorCount++
			continue
		}

		// The update logic is the same for all matched users: set the field to the correct string.
		updateDoc := bson.M{"$set": bson.M{"timezone": timezoneToSet}}

		result, err := userCollection.UpdateOne(ctx,
			bson.M{"_id": user.ObjectID},
			updateDoc,
		)
		if err != nil {
			log.Printf("Error updating user %s: %v", user.ObjectID.Hex(), err)
			errorCount++
			continue
		}

		if result.ModifiedCount > 0 {
			successCount++
			log.Printf("Fixed/Set timezone for user: %s (%s)",
				user.Name, user.ObjectID.Hex())
		}
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor iteration error: %w", err)
	}

	log.Printf("User timezone migration completed - Updated: %d, Errors: %d",
		successCount, errorCount)

	return nil
}

func (m *UpdateUserTimezone) Down(ctx context.Context) error {
	return nil
}
