// File: internal/notification/rules.go
package notification

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/notification"
)

// NotificationRule defines a push notification rule
type NotificationRule struct {
	ID         string
	Title      string // Added Title for consistency, you can adjust as needed.
	Message    string
	Priority   int // Lower number = higher priority
	IsEligible func(user *model.User, state *notification.UserActivityState, startOfToday time.Time) bool
}

// getNotificationRules returns rules based on notification type
func (f *FirebasePushNotifier) getNotificationRules(notificationType string) []NotificationRule {
	switch notificationType {
	case "unique":
		return f.getUniqueRules()
	case "recurrent":
		return f.getRecurrentRules()
	default:
		return []NotificationRule{}
	}
}

// getUniqueRules returns one-time notification rules based on the spreadsheet
func (f *FirebasePushNotifier) getUniqueRules() []NotificationRule {
	return []NotificationRule{
		{
			ID:       "DIAGNOSTICO_FINANCEIRO",
			Title:    "Seu diagnóstico te espera!",
			Message:  "Ei, seu diagnóstico tá esperando! Bora descobrir seu perfil financeiro? 🚀",
			Priority: 1,
			IsEligible: func(user *model.User, state *notification.UserActivityState, _ time.Time) bool {
				if _, sent := state.NotificationState.SentNotifications["DIAGNOSTICO_FINANCEIRO"]; sent {
					return false
				}
				// Trigger: "Não concluiu o diagnóstico", 1 day after starting
				return state.OnboardingState.CompletedDiagnosisAt.IsZero() &&
					!state.OnboardingState.StartedDiagnosisAt.IsZero() &&
					time.Since(state.OnboardingState.StartedDiagnosisAt) >= 24*time.Hour
			},
		},
		{
			ID:       "SONHAR",
			Title:    "Bora sonhar alto?",
			Message:  "Agora que você se conhece, bora sonhar alto? Registre seus sonhos! 🌈",
			Priority: 2,
			IsEligible: func(user *model.User, state *notification.UserActivityState, _ time.Time) bool {
				if _, sent := state.NotificationState.SentNotifications["SONHAR"]; sent {
					return false
				}
				// Trigger: "Finalizou o diagnóstico mas não criou sonhos", 1 day after finishing diagnosis
				return !state.OnboardingState.CompletedDiagnosisAt.IsZero() &&
					state.OnboardingState.CreatedFirstDreamAt.IsZero() &&
					time.Since(state.OnboardingState.CompletedDiagnosisAt) >= 24*time.Hour
			},
		},
		{
			ID:       "ORCAMENTO",
			Title:    "Organize a casa!",
			Message:  "Agora é hora de organizar a casa! Faz seu orçamento e bora realizar! 💪",
			Priority: 3,
			IsEligible: func(user *model.User, state *notification.UserActivityState, _ time.Time) bool {
				if _, sent := state.NotificationState.SentNotifications["ORCAMENTO"]; sent {
					return false
				}
				// Trigger: "Criou sonho, mas não montou orçamento", 1 day after creating first goal
				return !state.OnboardingState.CreatedFirstDreamAt.IsZero() &&
					state.OnboardingState.CreatedFirstBudgetAt.IsZero() &&
					time.Since(state.OnboardingState.CreatedFirstDreamAt) >= 24*time.Hour
			},
		},
	}
}

// getRecurrentRules returns recurring notification rules based on the spreadsheet
func (f *FirebasePushNotifier) getRecurrentRules() []NotificationRule {
	return []NotificationRule{
		{
			ID:       "HABITO_MENSAL",
			Title:    "Novo mês, nova chance!",
			Message:  "Novo mês, nova chance de brilhar! Já fez seu orçamento? ☑️",
			Priority: 1,
			IsEligible: func(user *model.User, state *notification.UserActivityState, _ time.Time) bool {
				// Condition 1: Check if today is the first business day of the month.
				// If not, the user is not eligible.
				if !isFirstBusinessDayOfMonth(time.Now()) {
					return false
				}

				// Condition 2 (Idempotency): Check if we have ALREADY sent this notification this month.
				lastSentTime, wasSentBefore := state.NotificationState.SentNotifications["HABITO_MENSAL"]
				if wasSentBefore {
					now := time.Now()
					// Check if the last notification was sent in the same month and year.
					if lastSentTime.Year() == now.Year() && lastSentTime.Month() == now.Month() {
						return false // Already sent this month, do not spam.
					}
				}

				// If we reach here, it's the first business day and we haven't sent the notification yet this month.
				return true
			},
		},
		{
			ID:       "INATIVIDADE",
			Title:    "Sentimos sua falta!",
			Message:  "Sentimos sua falta! Seus sonhos ainda tão aqui esperando você voltar! ✨",
			Priority: 2,
			IsEligible: func(user *model.User, state *notification.UserActivityState, _ time.Time) bool {
				// Condition 1: User must have logged in at least once.
				if state.LastLoginAt.IsZero() {
					return false
				}

				// Condition 2: The user must currently be inactive for at least 7 days.
				if time.Since(state.LastLoginAt) < 7*24*time.Hour {
					return false
				}

				// Condition 3: Check timing relative to the last time THIS notification was sent.
				// We are reusing the SentUnique map for this recurring rule.
				lastSentTime, wasSentBefore := state.NotificationState.SentNotifications["INATIVIDADE"]

				if !wasSentBefore {
					// Never sent before? They are eligible.
					return true
				}

				// Sent before? Check if it's been at least 7 days since the last send.
				return time.Since(lastSentTime) >= 7*24*time.Hour
			},
		},
		{
			ID:       "APONTAMENTO_DIARIO",
			Title:    "Não esqueça de anotar!",
			Message:  "Veja variações abaixo", // This is a placeholder
			Priority: 3,
			IsEligible: func(user *model.User, state *notification.UserActivityState, startOfToday time.Time) bool {
				// Condition 1: Prevent messaging new users who have never logged an expense.
				if state.LastTransactionLoggedAt.IsZero() {
					return false
				}

				// Condition 2: The user has NOT logged a transaction today.
				// If their last log was before the start of today, they are a candidate.
				hasLoggedTransactionToday := !state.LastTransactionLoggedAt.Before(startOfToday)
				if hasLoggedTransactionToday {
					return false
				}

				// Condition 3 (Idempotency): Check if we have ALREADY sent this reminder today.
				// This makes the rule safe to run multiple times.
				lastSentTime, wasSentBefore := state.NotificationState.SentNotifications["APONTAMENTO_DIARIO"]
				if wasSentBefore {
					// If it was sent before, check if that send occurred *after* the start of today.
					hasBeenRemindedToday := !lastSentTime.Before(startOfToday)
					if hasBeenRemindedToday {
						return false // Already reminded today, do not spam.
					}
				}

				// If we reach here, the user hasn't logged a transaction today AND hasn't been reminded yet.
				return true
			},
		},
	}
}

// findBestNotificationRule finds the highest priority eligible rule
func (f *FirebasePushNotifier) findBestNotificationRule(user *model.User, state *notification.UserActivityState, rules []NotificationRule, startOfToday time.Time) *NotificationRule {
	var bestRule *NotificationRule

	// We iterate through a copy to avoid modifying the original rule slice
	for i := range rules {
		rule := rules[i] // Make a copy to avoid pointer issues in loops
		if rule.IsEligible(user, state, startOfToday) {
			if bestRule == nil || rule.Priority < bestRule.Priority {
				bestRule = &rule
			}
		}
	}

	// Special handling for APONTAMENTO_DIARIO to set the correct daily message
	if bestRule != nil && bestRule.ID == "APONTAMENTO_DIARIO" {
		bestRule.Message = getDailyMessage()
	}

	return bestRule
}

// --- HELPER FUNCTIONS ---

// getDailyMessage returns a specific message based on the day of the week.
func getDailyMessage() string {
	switch time.Now().Weekday() {
	case time.Monday:
		return "Vai quebrar a sequência? Corre e anota seus gastos!💸"
	case time.Tuesday:
		return "Tá esquecendo de mim? Seus gastos de hoje tão no vácuo! 😉"
	case time.Wednesday:
		return "Só 1 min! Anota aí antes que o dia vire lenda 💰"
	case time.Thursday:
		return "Você promete que não vai me ignorar de novo, né? 👀"
	case time.Friday:
		return "O futuro te vê... não anotando os gastos. Bora mudar isso? 🔮"
	case time.Saturday:
		return "Tá com vergonha de anotar o que gastou no iFood? 😂 Bora lá!"
	case time.Sunday:
		return "Sequência linda a sua... seria uma pena se quebrasse 💔"
	default:
		return "Não se esqueça de anotar seus gastos de hoje!"
	}
}

// isFirstBusinessDayOfMonth checks if the given day is the first weekday of its month.
// By accepting a time.Time parameter, this function becomes easily testable.
func isFirstBusinessDayOfMonth(now time.Time) bool {
	// Quick check: If it's a weekend, it can't be a business day.
	if now.Weekday() == time.Saturday || now.Weekday() == time.Sunday {
		return false
	}

	// Truncate `now` to the beginning of the day for a clean comparison.
	year, month, day := now.Date()
	today := time.Date(year, month, day, 0, 0, 0, 0, now.Location())

	// Find the actual first business day of the month.
	firstOfMonth := time.Date(year, month, 1, 0, 0, 0, 0, now.Location())
	firstBusinessDay := firstOfMonth

	// Loop until we find a weekday.
	// This will run at most 3 times (e.g., if the 1st is a Saturday).
	for firstBusinessDay.Weekday() == time.Saturday || firstBusinessDay.Weekday() == time.Sunday {
		firstBusinessDay = firstBusinessDay.AddDate(0, 0, 1)
	}

	// Is today that day?
	return today.Equal(firstBusinessDay)
}
