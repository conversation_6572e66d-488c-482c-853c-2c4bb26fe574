package notification

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/notification"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

// New creates a new notification repository instance following established pattern
func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.NOTIFICATIONS_USER_ACTIVITY_STATES_COLLECTION),
	}

	// Create indexes following established pattern
	ctx := context.Background()

	// Unique index on userID field
	_, err := repo.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userId", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("userActivityState_userId"),
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on user_activity_states.userId field:", err)
	}

	// Additional indexes for efficient queries
	_, err = repo.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "lastLoginAt", Value: 1}},
			Options: options.Index().SetName("userActivityState_lastLoginAt"),
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on user_activity_states.lastLoginAt field:", err)
	}

	_, err = repo.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "lastTransactionLoggedAt", Value: 1}},
			Options: options.Index().SetName("userActivityState_lastTransactionLoggedAt"),
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on user_activity_states.lastTransactionLoggedAt field:", err)
	}

	return repo
}

// FindByUserID finds a user activity state by user ID
func (r *mongoDB) FindByUserID(ctx context.Context, userID string) (*notification.UserActivityState, error) {
	filter := bson.M{"userId": userID}

	var state notification.UserActivityState
	err := r.collection.FindOne(ctx, filter).Decode(&state)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "user activity state not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find user activity state", errors.Internal, err)
	}

	return state.Sanitize(), nil
}

// FindByUserIDs finds user activity states by multiple user IDs
func (r *mongoDB) FindByUserIDs(ctx context.Context, userIDs []string) ([]*notification.UserActivityState, error) {
	filter := bson.M{"userId": bson.M{"$in": userIDs}}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find user activity states", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var states []*notification.UserActivityState
	for cursor.Next(ctx) {
		var state notification.UserActivityState
		if err := cursor.Decode(&state); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode user activity state", errors.Internal, err)
		}
		states = append(states, state.Sanitize())
	}

	return states, nil
}

// FindEligibleUsers finds users matching specific criteria for notifications
func (r *mongoDB) FindEligibleUsers(ctx context.Context, criteria map[string]interface{}) ([]*notification.UserActivityState, error) {
	cursor, err := r.collection.Find(ctx, criteria)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find eligible users", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var states []*notification.UserActivityState
	for cursor.Next(ctx) {
		var state notification.UserActivityState
		if err := cursor.Decode(&state); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode user activity state", errors.Internal, err)
		}
		states = append(states, state.Sanitize())
	}

	return states, nil
}

// Create creates a new user activity state
func (r *mongoDB) Create(ctx context.Context, state *notification.UserActivityState) error {
	state.PrepareCreate()

	_, err := r.collection.InsertOne(ctx, state)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "user activity state already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create user activity state", errors.Internal, err)
	}

	return nil
}

// Update updates an existing user activity state
func (r *mongoDB) Update(ctx context.Context, state *notification.UserActivityState) error {
	state.PrepareUpdate()

	filter := bson.M{"userId": state.UserID}
	update := bson.M{"$set": state}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update user activity state", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "user activity state not found", errors.NotFound, nil)
	}

	return nil
}

// UpdateLastLogin updates the last login timestamp for a user
func (r *mongoDB) UpdateLastLogin(ctx context.Context, userID string) error {
	filter := bson.M{"userId": userID}
	update := bson.M{
		"$set": bson.M{
			"lastLoginAt": time.Now(),
			"updatedAt":   time.Now(),
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		return errors.New(errors.Repository, "failed to update last login", errors.Internal, err)
	}

	return nil
}

// UpdateLastTransactionLogged updates the last expense logged timestamp for a user
func (r *mongoDB) UpdateLastTransactionLogged(ctx context.Context, userID string) error {
	filter := bson.M{"userId": userID}
	update := bson.M{
		"$set": bson.M{
			"lastTransactionLoggedAt": time.Now(),
			"updatedAt":               time.Now(),
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		return errors.New(errors.Repository, "failed to update last expense logged", errors.Internal, err)
	}

	return nil
}

// UpdateLastProgressionActivity updates the last progression activity timestamp for a user
func (r *mongoDB) UpdateLastProgressionActivity(ctx context.Context, userID string) error {
	filter := bson.M{"userId": userID}
	update := bson.M{
		"$set": bson.M{
			"lastProgressionActivity": time.Now(),
			"updatedAt":               time.Now(),
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		return errors.New(errors.Repository, "failed to update last progression activity", errors.Internal, err)
	}

	return nil
}

// UpdateNotificationSent records that a notification was sent to a user
func (r *mongoDB) UpdateNotificationSent(ctx context.Context, userID, notificationID string) error {
	filter := bson.M{"userId": userID}
	update := bson.M{
		"$set": bson.M{
			fmt.Sprintf("notifications.sentNotifications.%s", notificationID): time.Now(),
			"notifications.lastSentAt":                                        time.Now(),
			"updatedAt":                                                       time.Now(),
		},
		"$inc": bson.M{
			"notifications.totalSent": 1,
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		return errors.New(errors.Repository, "failed to update notification sent", errors.Internal, err)
	}

	return nil
}

// UpdateOnboardingEvent updates onboarding event timestamps
func (r *mongoDB) UpdateOnboardingEvent(ctx context.Context, userID, eventType string) error {
	filter := bson.M{"userId": userID}

	var fieldName string
	switch eventType {
	case "started_diagnosis":
		fieldName = "onboarding.startedDiagnosisAt"
	case "completed_diagnosis":
		fieldName = "onboarding.completedDiagnosisAt"
	case "created_first_dream":
		fieldName = "onboarding.createdFirstDreamAt"
	case "created_first_budget":
		fieldName = "onboarding.createdFirstBudgetAt"
	case "completed_first_trail":
		fieldName = "onboarding.completedFirstTrailAt"
	default:
		return errors.New(errors.Repository, "invalid onboarding event type", errors.BadRequest, nil)
	}

	update := bson.M{
		"$set": bson.M{
			fieldName:   time.Now(),
			"updatedAt": time.Now(),
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		return errors.New(errors.Repository, "failed to update onboarding event", errors.Internal, err)
	}

	return nil
}

// Upsert creates or updates a user activity state
func (r *mongoDB) Upsert(ctx context.Context, state *notification.UserActivityState) error {
	state.PrepareUpdate()

	filter := bson.M{"userId": state.UserID}
	update := bson.M{"$set": state}

	_, err := r.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		return errors.New(errors.Repository, "failed to upsert user activity state", errors.Internal, err)
	}

	return nil
}

// Delete removes a user activity state
func (r *mongoDB) Delete(ctx context.Context, userID string) error {
	filter := bson.M{"userId": userID}

	result, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		return errors.New(errors.Repository, "failed to delete user activity state", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "user activity state not found", errors.NotFound, nil)
	}

	return nil
}
