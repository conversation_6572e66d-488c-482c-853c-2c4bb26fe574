package apple

import (
	"context"
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/apple"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing"
	"github.com/dsoplabs/dinbora-backend/internal/service/firebase"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// OAuth
	HandleAppleRegister() echo.HandlerFunc
	HandleAppleLogin() echo.HandlerFunc
}

type controller struct {
	Service         apple.Service
	FirebaseService firebase.Service
}

func New(userService user.Service, billingService billing.Service, s3Service s3.Service, firebaseService firebase.Service) Controller {
	return &controller{
		Service:         apple.New(userService, billingService, s3Service),
		FirebaseService: firebaseService,
	}
}

// Routes
func (ac *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	authGroup := currentGroup.Group("/auth")
	authGroup.POST("/oauth2/callback/apple/register", ac.HandleAppleRegister())
	authGroup.POST("/oauth2/callback/apple/login", ac.HandleAppleLogin())
}

// Helper
// StringToByte converts a string to a byte (uint8).
// Returns the converted byte and a boolean indicating success.
// An empty string is treated as a failed conversion in this version.
func StringToByte(s string) (byte, bool) {
	if s == "" {
		return 0, false // Treat empty string as a conversion failure
	}
	val, err := strconv.ParseUint(s, 10, 8) // Parse as uint8 (byte)
	if err != nil {
		return 0, false // Conversion failed
	}
	return byte(val), true // Conversion successful
}

// OAuth
func (ac *controller) HandleAppleRegister() echo.HandlerFunc {
	return func(c echo.Context) error {
		referralCode := c.QueryParam("referral")

		// Get access token from form
		accessToken := c.FormValue("access")
		if accessToken == "" {
			return errors.NewValidationError(errors.Controller, "access token is required", errors.KeyAppleErrorAccessTokenRequired, nil)
		}

		// Get FCM token from form
		fcmToken := c.FormValue("fcm")

		// Get timezone from context
		userTimezone := c.Get("timezone").(*time.Location)

		// Get name from form
		name := c.FormValue("name")
		lastName := c.FormValue("lastName")

		// Get the register source from form
		registerSource := c.FormValue("registerSource")

		// Parse onboarding data from form (following auth pattern)
		var onboarding *model.Onboarding
		ageRangeIDStr := c.FormValue("onboarding[ageRange]")
		financialSituationIDStr := c.FormValue("onboarding[financialSituation]")
		financialGoalIDStr := c.FormValue("onboarding[financialGoal]")
		firstPersonalInterestIDStr := c.FormValue("onboarding[personalInterests][0]")

		// Initialize Onboarding only if there's an indication of its data
		if ageRangeIDStr != "" || financialSituationIDStr != "" || financialGoalIDStr != "" || firstPersonalInterestIDStr != "" {
			onboarding = new(model.Onboarding)

			if idVal, ok := StringToByte(ageRangeIDStr); ok {
				onboarding.AgeRange.ID = idVal
			} else if ageRangeIDStr != "" {
				return errors.NewValidationError(errors.Controller, "invalid format for onboarding[ageRange]", errors.KeyAppleErrorInvalidOboardingAgeRange, nil)
			}

			if idVal, ok := StringToByte(financialSituationIDStr); ok {
				onboarding.FinancialSituation.ID = idVal
			} else if financialSituationIDStr != "" {
				return errors.NewValidationError(errors.Controller, "invalid format for onboarding[financialSituation]", errors.KeyAppleErrorInvalidOboardingFinancialSituation, nil)
			}

			if idVal, ok := StringToByte(financialGoalIDStr); ok {
				onboarding.FinancialGoal.ID = idVal
			} else if financialGoalIDStr != "" {
				return errors.NewValidationError(errors.Controller, "invalid format for onboarding[financialGoal]", errors.KeyAppleErrorInvalidOboardingFinancialGoal, nil)
			}

			var interests []model.PersonalInterests
			idx := 0
			for {
				interestKey := fmt.Sprintf("onboarding[personalInterests][%d]", idx)
				interestIDStr := c.FormValue(interestKey)
				if interestIDStr == "" {
					break
				}

				if idVal, ok := StringToByte(interestIDStr); ok {
					interests = append(interests, model.PersonalInterests{ID: idVal})
				} else {
					errMsg := fmt.Sprintf("invalid format for %s", interestKey)
					return errors.NewValidationError(errors.Controller, errors.Message(errMsg), errors.KeyAppleErrorInvalidOboardingPersonalInterest, nil)
				}
				idx++
				if idx > 50 {
					break
				}
			}
			onboarding.PersonalInterests = interests
		} else {
			onboarding = &model.Onboarding{}
		}

		// Get photo from request (following auth pattern)
		photo, err := c.FormFile("photo")
		if err != nil && err != http.ErrMissingFile {
			return errors.NewValidationError(errors.Controller, "failed to process photo", errors.KeyAppleErrorFailedToProcessPhoto, err)
		}

		var photoURL string
		if photo != nil {
			// Validate file type
			ext := strings.ToLower(filepath.Ext(photo.Filename))
			allowedExts := map[string]bool{
				".jpg":  true,
				".jpeg": true,
				".png":  true,
				".heic": true,
			}
			if !allowedExts[ext] {
				return errors.NewValidationError(errors.Controller, "invalid file type, only JPG, JPEG, PNG and HEIC are allowed", errors.KeyAppleErrorInvalidFileType, nil)
			}

			// Validate file size (max 5MB)
			if photo.Size > 5*1024*1024 {
				return errors.NewValidationError(errors.Controller, "file too large, max size is 5MB", errors.KeyAppleErrorFileTooLarge, nil)
			}
		}

		appleUserDetails, err := ac.Service.HandleAppleCallback(accessToken)
		if err != nil {
			return err
		}

		// Add name to the Apple User Detail before call Register.
		appleUserDetails.Name = name
		appleUserDetails.LastName = lastName
		appleUserDetails.RegisterSource = registerSource
		appleUserDetails.Timezone = userTimezone.String()

		accessData, err := ac.Service.Register(*appleUserDetails, onboarding, photo, photoURL, referralCode)
		if err != nil {
			return err
		}

		// Get user ID from access token
		userToken, err := token.GetClaimsFromToken(accessData.Access)
		if err != nil {
			return err
		}

		if fcmToken != "" {
			err := ac.FirebaseService.Create(c.Request().Context(), userToken.Uid, fcmToken)
			if err != nil {
				return err
			}
		}

		return c.JSON(http.StatusCreated, accessData)
	}
}

func (ac *controller) HandleAppleLogin() echo.HandlerFunc {
	return func(c echo.Context) error {
		var callbackRequest struct {
			Access   string `json:"access"`
			FCMToken string `json:"fcm"`
		}

		if err := c.Bind(&callbackRequest); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid apple login input", errors.KeyAppleErrorInvalidLoginInput, err)
		}

		appleUserDetails, err := ac.Service.HandleAppleCallback(callbackRequest.Access)
		if err != nil {
			return err
		}

		user, token, err := ac.Service.Login(*appleUserDetails)
		if err != nil {
			return err
		}

		loginResponse := LoginResponseDTO{
			ID:           user.ID,
			Name:         user.Name,
			LastName:     user.LastName,
			Email:        user.Email,
			PhotoURL:     user.PhotoURL,
			ReferralCode: user.ReferralCode,
			Access:       token.Access,
			Refresh:      token.Refresh,
		}

		if callbackRequest.FCMToken != "" {
			err := ac.FirebaseService.Create(c.Request().Context(), user.ID, callbackRequest.FCMToken)
			if err != nil {
				return err
			}
		}

		return c.JSON(http.StatusOK, loginResponse)
	}
}
