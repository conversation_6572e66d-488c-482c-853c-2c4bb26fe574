package migrations

import (
	"context"
	"fmt"
	"log"
	"os"
	"strconv"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/dsoplabs/dinbora-backend/internal/service/notification"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type AddUserBrevoList struct {
	db                  *mongo.Database
	notificationService *notification.Service
	brevoUserListID     int64
}

func NewAddUserBrevoList(db *mongo.Database, notificationService *notification.Service) *AddUserBrevoList {
	brevoListID, _ := strconv.ParseInt(os.Getenv("BREVO_USER_LIST_ID"), 10, 64)
	log.Printf("Brevo list ID: %d", brevoListID)
	return &AddUserBrevoList{
		db:                  db,
		notificationService: notificationService,
		brevoUserListID:     brevoListID}
}

func (m *AddUserBrevoList) Name() string {
	return "add_user_brevo_list_2025_09_25_fix000"
}

func (m *AddUserBrevoList) Up(ctx context.Context) error {
	log.Println("Starting Add User Brevo List Migration...")

	// Get all users
	userCollection := m.db.Collection(repository.USERS_COLLECTION)
	cursor, err := userCollection.Find(ctx, bson.D{})
	if err != nil {
		return fmt.Errorf("failed to find users: %w", err)
	}
	defer cursor.Close(ctx)

	successCount := 0
	errorCount := 0

	for cursor.Next(ctx) {
		var user model.User
		if err := cursor.Decode(&user); err != nil {
			log.Printf("Error decoding user: %v", err)
			errorCount++
			continue
		}

		// Add user to Brevo list
		// For testing only don't update user with email "<EMAIL>", "<EMAIL>", "<EMAIL>".
		if user.Email != "<EMAIL>" && user.Email != "<EMAIL>" && user.Email != "<EMAIL>" {
			m.notificationService.BrevoNotifier.AddUserToList(ctx, &user, m.brevoUserListID)
			log.Printf("Added user %s with email %s to Brevo list %d", user.Name, user.Email, m.brevoUserListID)
		} else {
			log.Printf("Skipping user %s with email %s", user.Name, user.Email)
		}

		successCount++
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor iteration error: %w", err)
	}

	log.Printf("Add User Brevo List Migration completed - Added: %d, Errors: %d",
		successCount, errorCount)

	return nil
}

func (m *AddUserBrevoList) Down(ctx context.Context) error {
	return nil
}
