package middlewares

import (
	"log"
	"time"

	"github.com/labstack/echo/v4"
)

const timezoneKey = "timezone"

// TimezoneMiddleware returns a middleware that adds the timezone to the context
func TimezoneMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			timezoneHeader := c.Request().Header.Get("X-User-Timezone")

			var loc *time.Location
			var err error

			if timezoneHeader != "" {
				loc, err = time.LoadLocation(timezoneHeader)
				if err != nil {
					log.Printf("Warning: invalid timezone '%s': %v. Falling back to default.\n", timezoneHeader, err)
				}
			}

			// fallback to Sao Paulo if missing or invalid
			if loc == nil {
				loc, err = time.LoadLocation("America/Sao_Paulo")
				if err != nil {
					// This should not happen, but fallback to UTC if it does
					log.Printf("Error: could not load default Sao Paulo timezone: %v. Falling back to UTC.\n", err)
					loc = time.UTC
				}
			}

			// Store timezone in Echo context
			c.Set(timezoneKey, loc)

			return next(c)
		}
	}
}
