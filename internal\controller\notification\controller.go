package notification

import (
	"context"
	"net/http"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/notification"
	_notification "github.com/dsoplabs/dinbora-backend/internal/service/notification"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// Push notification endpoints
	ProcessScheduledNotifications() echo.HandlerFunc
	SendTestNotification() echo.HandlerFunc
	GetUserActivityState() echo.HandlerFunc
}

type controller struct {
	NotificationService *_notification.Service
}

func New(notificationService *_notification.Service) Controller {
	return &controller{
		NotificationService: notificationService,
	}
}

// RegisterRoutes registers notification routes following established pattern
func (nc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	notificationGroup := currentGroup.Group("/notifications")

	// Scheduled notification processing (protected by service auth)
	notificationGroup.POST("/process", nc.ProcessScheduledNotifications(), middlewares.EventBridgePushNotificationMiddleware())

	// Test endpoint for development (protected by admin auth)
	notificationGroup.POST("/test", nc.SendTestNotification(), middlewares.AuthGuard(), middlewares.UserContextMiddleware(), middlewares.AdminOnly())

	// User activity state endpoint (protected by user auth)
	notificationGroup.GET("/activity-state", nc.GetUserActivityState(), middlewares.AuthGuard(), middlewares.UserContextMiddleware())
}

// ProcessScheduledNotifications handles scheduled notification processing requests
func (nc *controller) ProcessScheduledNotifications() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Parse request body
		var req ProcessNotificationsRequestDTO
		if err := c.Bind(&req); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid request body", errors.KeyNotificationErrorInvalidRequest, err)
		}

		if err := c.Validate(&req); err != nil {
			return errors.NewValidationError(errors.Controller, "request validation failed", errors.KeyNotificationErrorValidationFailed, err)
		}

		// Validate notification type
		if req.Type != "unique" && req.Type != "recurrent" {
			return errors.NewValidationError(errors.Controller, "invalid notification type", errors.KeyNotificationErrorInvalidType, nil)
		}

		// Check if push notification service is available
		if nc.NotificationService.PushNotifier == nil {
			return errors.New(errors.Controller, "push notification service not available", errors.ServiceUnavailable, nil)
		}

		// Process notifications asynchronously to avoid timeout
		// Use background context to avoid cancellation when request completes
		go func() {
			backgroundCtx := context.Background()
			err := nc.NotificationService.PushNotifier.ProcessScheduledNotifications(backgroundCtx, req.Type)
			if err != nil {
				c.Logger().Errorf("Failed to process scheduled notifications: %v", err)
			}
		}()

		return c.JSON(http.StatusAccepted, ProcessNotificationsResponseDTO{
			Status:  "accepted",
			Message: "Notification processing started",
			Type:    req.Type,
		})
	}
}

// SendTestNotification sends a test notification (admin only)
func (nc *controller) SendTestNotification() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var req SendTestNotificationRequestDTO
		if err := c.Bind(&req); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid request body", errors.KeyNotificationErrorInvalidRequest, err)
		}

		if err := c.Validate(&req); err != nil {
			return errors.NewValidationError(errors.Controller, "request validation failed", errors.KeyNotificationErrorValidationFailed, err)
		}

		// Send test notification
		if nc.NotificationService.PushNotifier != nil {
			data := map[string]string{
				"type":      "test",
				"timestamp": time.Now().Format(time.RFC3339),
			}

			err := nc.NotificationService.PushNotifier.SendPushNotification(
				ctx,
				req.UserID,
				req.Title,
				req.Message,
				data,
			)
			if err != nil {
				return err
			}
		} else {
			return errors.New(errors.Controller, "push notification service not available", errors.ServiceUnavailable, nil)
		}

		return c.JSON(http.StatusOK, SendTestNotificationResponseDTO{
			Status:  "sent",
			Message: "Test notification sent successfully",
			UserID:  req.UserID,
		})
	}
}

// GetUserActivityState returns the activity state for the authenticated user
func (nc *controller) GetUserActivityState() echo.HandlerFunc {
	return func(c echo.Context) error {
		//ctx := c.Request().Context()

		// Get user ID from token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user activity state (this will need to be implemented in the service)
		// For now, we'll return a placeholder response
		// TODO: Implement GetUserActivityState in the notification service

		return c.JSON(http.StatusOK, GetUserActivityStateResponseDTO{
			UserID:                  userToken.Uid,
			LastLoginAt:             time.Now(),
			LastTransactionLoggedAt: time.Time{},
			LastProgressionActivity: time.Time{},
			OnboardingState: OnboardingStateDTO{
				StartedDiagnosisAt:    time.Time{},
				CompletedDiagnosisAt:  time.Time{},
				CreatedFirstDreamAt:   time.Time{},
				CreatedFirstBudgetAt:  time.Time{},
				CompletedFirstTrailAt: time.Time{},
			},
			NotificationState: NotificationStateDTO{
				SentNotifications: make(map[string]time.Time),
				LastSentAt:        time.Time{},
				TotalSent:         0,
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		})
	}
}

// Helper function to convert model to DTO
func (nc *controller) convertToDTO(state *notification.UserActivityState) GetUserActivityStateResponseDTO {
	return GetUserActivityStateResponseDTO{
		UserID:                  state.UserID,
		LastLoginAt:             state.LastLoginAt,
		LastTransactionLoggedAt: state.LastTransactionLoggedAt,
		LastProgressionActivity: state.LastProgressionActivity,
		OnboardingState: OnboardingStateDTO{
			StartedDiagnosisAt:    state.OnboardingState.StartedDiagnosisAt,
			CompletedDiagnosisAt:  state.OnboardingState.CompletedDiagnosisAt,
			CreatedFirstDreamAt:   state.OnboardingState.CreatedFirstDreamAt,
			CreatedFirstBudgetAt:  state.OnboardingState.CreatedFirstBudgetAt,
			CompletedFirstTrailAt: state.OnboardingState.CompletedFirstTrailAt,
		},
		NotificationState: NotificationStateDTO{
			SentNotifications: state.NotificationState.SentNotifications,
			LastSentAt:        state.NotificationState.LastSentAt,
			TotalSent:         state.NotificationState.TotalSent,
		},
		CreatedAt: state.CreatedAt,
		UpdatedAt: state.UpdatedAt,
	}
}
