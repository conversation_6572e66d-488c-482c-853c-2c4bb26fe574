package migrations

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// FixUserCreatedAtFromObjectID is a migration to fix user documents
// that have a zero-value or missing CreatedAt timestamp. It extracts
// the correct timestamp from the MongoDB ObjectID and updates the document.
type FixUserCreatedAtFromObjectID struct {
	db *mongo.Database
}

// NewFixUserCreatedAtFromObjectID creates a new instance of the migration.
func NewFixUserCreatedAtFromObjectID(db *mongo.Database) *FixUserCreatedAtFromObjectID {
	return &FixUserCreatedAtFromObjectID{
		db: db,
	}
}

// Name returns the unique name of the migration.
func (m *FixUserCreatedAtFromObjectID) Name() string {
	// Use a descriptive name with a date to ensure it's unique
	return "user_createdat_from_objectid_2025_09_25_fix000"
}

// Up runs the migration to fix the CreatedAt timestamps.
func (m *FixUserCreatedAtFromObjectID) Up(ctx context.Context) error {
	log.Println("Starting FixUserCreatedAtFromObjectID migration...")

	userCollection := m.db.Collection(repository.USERS_COLLECTION)

	// The zero value for time.Time in Go corresponds to "0001-01-01T00:00:00.000Z"
	zeroTime := time.Time{}

	// Filter for documents where 'createdAt' is the zero value or doesn't exist.
	// This makes the migration more robust.
	filter := bson.M{
		"$or": []bson.M{
			{"createdAt": zeroTime},
			{"createdAt": bson.M{"$exists": false}},
		},
	}

	cursor, err := userCollection.Find(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to find users with zero-value createdAt: %w", err)
	}
	defer cursor.Close(ctx)

	successCount := 0
	errorCount := 0

	for cursor.Next(ctx) {
		var user model.User
		if err := cursor.Decode(&user); err != nil {
			log.Printf("Error decoding user: %v", err)
			errorCount++
			continue
		}

		// MongoDB's ObjectID contains an embedded timestamp of its creation.
		// We can extract it to get the real creation time.
		creationTime := user.ObjectID.Timestamp()

		log.Printf("Fixing user ID %s. Setting CreatedAt to %s", user.ObjectID.Hex(), creationTime.String())

		// Create the update operation to set the 'createdat' field
		update := bson.M{
			"$set": bson.M{"createdAt": creationTime},
		}

		// Update the specific user document by its ID
		_, err := userCollection.UpdateOne(ctx, bson.M{"_id": user.ObjectID}, update)
		if err != nil {
			log.Printf("Failed to update user with ID %s: %v", user.ObjectID.Hex(), err)
			errorCount++
			continue
		}

		successCount++
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor iteration error: %w", err)
	}

	log.Printf("FixUserCreatedAtFromObjectID migration completed - Fixed: %d, Errors: %d",
		successCount, errorCount)

	return nil
}

// Down is a no-op for this migration as reverting the correct timestamps
// would be destructive.
func (m *FixUserCreatedAtFromObjectID) Down(ctx context.Context) error {
	log.Println("Down migration for FixUserCreatedAtFromObjectID is not implemented.")
	return nil
}
