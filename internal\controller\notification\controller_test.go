package notification

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"sync"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/service/notification"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockNotificationService mocks the notification service
type MockNotificationService struct {
	mock.Mock
	PushNotifier MockPushNotifier
}

// MockPushNotifier mocks the push notifier
type MockPushNotifier struct {
	mock.Mock
	mu               sync.Mutex
	processCallCount int
	processWaitGroup sync.WaitGroup
}

func (m *MockPushNotifier) SendPushNotification(ctx context.Context, userID, title, message string, data map[string]string) error {
	args := m.Called(ctx, userID, title, message, data)
	return args.Error(0)
}

func (m *MockPushNotifier) ProcessScheduledNotifications(ctx context.Context, notificationType string) error {
	m.mu.Lock()
	m.processCallCount++
	m.mu.Unlock()

	// Signal that the goroutine has been called
	defer m.processWaitGroup.Done()

	args := m.Called(ctx, notificationType)
	return args.Error(0)
}

func (m *MockPushNotifier) UpdateLastLogin(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockPushNotifier) UpdateLastTransactionLogged(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockPushNotifier) UpdateLastProgressionActivity(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockPushNotifier) UpdateOnboardingEvent(ctx context.Context, userID, eventType string) error {
	args := m.Called(ctx, userID, eventType)
	return args.Error(0)
}

// ExpectProcessCall sets up expectation for ProcessScheduledNotifications and prepares wait group
func (m *MockPushNotifier) ExpectProcessCall() {
	m.processWaitGroup.Add(1)
}

// WaitForProcessCall waits for the ProcessScheduledNotifications call to complete
func (m *MockPushNotifier) WaitForProcessCall(timeout time.Duration) bool {
	done := make(chan struct{})
	go func() {
		m.processWaitGroup.Wait()
		close(done)
	}()

	select {
	case <-done:
		return true
	case <-time.After(timeout):
		return false
	}
}

// ResetProcessCall resets the process call tracking
func (m *MockPushNotifier) ResetProcessCall() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.processCallCount = 0
	m.processWaitGroup = sync.WaitGroup{}
}

// TestProcessScheduledNotifications tests the scheduled notification processing endpoint
type dummyValidator struct{}

func (v *dummyValidator) Validate(i interface{}) error {
	// Validate ProcessNotificationsRequestDTO
	if req, ok := i.(*ProcessNotificationsRequestDTO); ok {
		if req.Type == "" {
			return errors.NewValidationError(errors.Controller, "type is required", errors.KeyNotificationErrorValidationFailed, nil)
		}
		if req.Type != "unique" && req.Type != "recurrent" {
			return errors.NewValidationError(errors.Controller, "invalid notification type", errors.KeyNotificationErrorInvalidType, nil)
		}
	}

	// Validate SendTestNotificationRequestDTO
	if req, ok := i.(*SendTestNotificationRequestDTO); ok {
		if req.UserID == "" {
			return errors.NewValidationError(errors.Controller, "userID is required", errors.KeyNotificationErrorValidationFailed, nil)
		}
		if req.Title == "" {
			return errors.NewValidationError(errors.Controller, "title is required", errors.KeyNotificationErrorValidationFailed, nil)
		}
		if req.Message == "" {
			return errors.NewValidationError(errors.Controller, "message is required", errors.KeyNotificationErrorValidationFailed, nil)
		}
	}

	return nil
}

type NoopPushNotifier struct{}

func (n *NoopPushNotifier) SendPushNotification(ctx context.Context, userID, title, message string, data map[string]string) error {
	return nil
}
func (n *NoopPushNotifier) ProcessScheduledNotifications(ctx context.Context, notificationType string) error {
	return nil
}
func (n *NoopPushNotifier) UpdateLastLogin(ctx context.Context, userID string) error {
	return nil
}
func (n *NoopPushNotifier) UpdateLastTransactionLogged(ctx context.Context, userID string) error {
	return nil
}
func (n *NoopPushNotifier) UpdateLastProgressionActivity(ctx context.Context, userID string) error {
	return nil
}
func (n *NoopPushNotifier) UpdateOnboardingEvent(ctx context.Context, userID, eventType string) error {
	return nil
}

func TestProcessScheduledNotifications(t *testing.T) {
	e := echo.New()
	e.Validator = &dummyValidator{}
	mockPushNotifier := new(MockPushNotifier)
	notificationService := &notification.Service{PushNotifier: mockPushNotifier}
	ctrl := New(notificationService)

	testCases := []struct {
		name                 string
		body                 ProcessNotificationsRequestDTO
		mockSetup            func(*MockPushNotifier)
		expectedStatus       int
		expectedResponse     interface{}
		expectError          bool
		expectedErrorMessage string
		expectAsyncCall      bool
	}{
		{
			name: "ValidRequest - unique",
			body: ProcessNotificationsRequestDTO{Type: "unique"},
			mockSetup: func(mockPush *MockPushNotifier) {
				mockPush.ExpectProcessCall()
				mockPush.On("ProcessScheduledNotifications", mock.Anything, "unique").Return(nil).Once()
			},
			expectedStatus: http.StatusAccepted,
			expectedResponse: ProcessNotificationsResponseDTO{
				Status:  "accepted",
				Message: "Notification processing started",
				Type:    "unique",
			},
			expectAsyncCall: true,
		},
		{
			name: "ValidRequest - recurrent",
			body: ProcessNotificationsRequestDTO{Type: "recurrent"},
			mockSetup: func(mockPush *MockPushNotifier) {
				mockPush.ExpectProcessCall()
				mockPush.On("ProcessScheduledNotifications", mock.Anything, "recurrent").Return(nil).Once()
			},
			expectedStatus: http.StatusAccepted,
			expectedResponse: ProcessNotificationsResponseDTO{
				Status:  "accepted",
				Message: "Notification processing started",
				Type:    "recurrent",
			},
			expectAsyncCall: true,
		},
		{
			name:            "InvalidType",
			body:            ProcessNotificationsRequestDTO{Type: "invalid"},
			mockSetup:       func(mockPush *MockPushNotifier) {},
			expectedStatus:  http.StatusBadRequest,
			expectError:     true,
			expectAsyncCall: false,
		},
		{
			name:            "InvalidRequestBody",
			body:            ProcessNotificationsRequestDTO{},
			mockSetup:       func(mockPush *MockPushNotifier) {},
			expectedStatus:  http.StatusBadRequest,
			expectError:     true,
			expectAsyncCall: false,
		},
		{
			name: "ServiceUnavailable",
			body: ProcessNotificationsRequestDTO{Type: "unique"},
			mockSetup: func(mockPush *MockPushNotifier) {
				// This test case will set PushNotifier to nil in the test loop
			},
			expectedStatus:  http.StatusServiceUnavailable,
			expectError:     true,
			expectAsyncCall: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Reset the mock before each test
			mockPushNotifier.Mock = mock.Mock{}
			mockPushNotifier.ResetProcessCall()

			// Set up the notifier based on test case
			if tc.name == "ServiceUnavailable" {
				ctrl.(*controller).NotificationService.PushNotifier = nil
			} else {
				ctrl.(*controller).NotificationService.PushNotifier = mockPushNotifier
			}

			// Set up mock expectations
			tc.mockSetup(mockPushNotifier)

			jsonBody, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPost, "/notifications/process", bytes.NewReader(jsonBody))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			handler := ctrl.ProcessScheduledNotifications()
			err := handler(c)

			if tc.expectError {
				// For error cases, the handler should return an error that gets converted to HTTP status
				assert.Error(t, err)
				// The error should be a domain error that maps to the expected status
				if domainErr, ok := err.(*errors.DomainError); ok {
					restErr := errors.HTTPStatus(domainErr)
					assert.Equal(t, tc.expectedStatus, restErr.Code())
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedStatus, rec.Code)
				if tc.expectedResponse != nil {
					var response ProcessNotificationsResponseDTO
					err = json.Unmarshal(rec.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.Equal(t, tc.expectedResponse, response)
				}
			}

			// Wait for async call if expected
			if tc.expectAsyncCall {
				success := mockPushNotifier.WaitForProcessCall(2 * time.Second)
				assert.True(t, success, "Expected async ProcessScheduledNotifications call did not complete within timeout")
			}

			// Assert mock expectations
			mockPushNotifier.AssertExpectations(t)
		})
	}
}

// TestSendTestNotification tests the test notification endpoint
func TestSendTestNotification(t *testing.T) {
	e := echo.New()
	e.Validator = &dummyValidator{}
	mockPushNotifier := new(MockPushNotifier)
	notificationService := &notification.Service{PushNotifier: mockPushNotifier}
	ctrl := New(notificationService)

	testCases := []struct {
		name             string
		body             interface{}
		mockSetup        func()
		expectedStatus   int
		expectedResponse interface{}
		expectError      bool
	}{
		{
			name: "ValidRequest",
			body: SendTestNotificationRequestDTO{
				UserID:  "test-user-123",
				Title:   "Test Notification",
				Message: "This is a test message",
			},
			mockSetup: func() {
				mockPushNotifier.On("SendPushNotification", mock.Anything, "test-user-123", "Test Notification", "This is a test message", mock.Anything).Return(nil).Once()
			},
			expectedStatus: http.StatusOK,
			expectedResponse: SendTestNotificationResponseDTO{
				Status:  "sent",
				Message: "Test notification sent successfully",
				UserID:  "test-user-123",
			},
		},
		{
			name: "MissingUserID",
			body: SendTestNotificationRequestDTO{
				Title:   "Test Notification",
				Message: "This is a test message",
			},
			mockSetup:      func() {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name: "ServiceUnavailable",
			body: SendTestNotificationRequestDTO{
				UserID:  "test-user-123",
				Title:   "Test Notification",
				Message: "This is a test message",
			},
			mockSetup: func() {
				// Detach the notifier for this test case
				ctrl.(*controller).NotificationService.PushNotifier = nil
			},
			expectedStatus: http.StatusServiceUnavailable,
			expectError:    true,
		},
		{
			name: "ErrorSending",
			body: SendTestNotificationRequestDTO{
				UserID:  "test-user-123",
				Title:   "Test Notification",
				Message: "This is a test message",
			},
			mockSetup: func() {
				mockPushNotifier.On("SendPushNotification", mock.Anything, "test-user-123", "Test Notification", "This is a test message", mock.Anything).Return(assert.AnError).Once()
			},
			expectedStatus: http.StatusInternalServerError,
			expectError:    true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.name == "MissingUserID" {
				ctrl.(*controller).NotificationService.PushNotifier = &NoopPushNotifier{}
			} else if tc.name == "ServiceUnavailable" {
				// This will be set in mockSetup
			} else {
				ctrl.(*controller).NotificationService.PushNotifier = mockPushNotifier
			}
			mockPushNotifier.Mock = mock.Mock{} // Reset mock expectations

			tc.mockSetup()

			jsonBody, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPost, "/notifications/test", bytes.NewReader(jsonBody))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			handler := ctrl.SendTestNotification()
			err := handler(c)

			if tc.expectError {
				// For error cases, the handler should return an error that gets converted to HTTP status
				assert.Error(t, err)
				// The error should be a domain error that maps to the expected status
				if domainErr, ok := err.(*errors.DomainError); ok {
					restErr := errors.HTTPStatus(domainErr)
					assert.Equal(t, tc.expectedStatus, restErr.Code())
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedStatus, rec.Code)
				if tc.expectedResponse != nil {
					var response SendTestNotificationResponseDTO
					err = json.Unmarshal(rec.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.Equal(t, tc.expectedResponse, response)
				}
			}

			// Only assert expectations if we're using the mock
			if tc.name != "MissingUserID" && tc.name != "ServiceUnavailable" {
				mockPushNotifier.AssertExpectations(t)
			}
		})
	}
}
