package user

import (
	"context"
	"os"
	"strconv"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialdna"
	"github.com/nyaruka/phonenumbers"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CRUD
func (s *service) Create(ctx context.Context, user *model.User, referralCode string) error {
	foundUser, err := s.Repository.FindByEmail(ctx, user.Email)
	if err != nil {
		// If it's a "not found" error, continue with user creation
		if domainErr, ok := err.(*errors.DomainError); !ok || domainErr.Kind() != errors.NotFound {
			// Stop creation for any other type of error (internal errors, etc.), propagate it.
			return err
		}
	} else if foundUser != nil {
		return errors.NewConflictError(errors.Service, "user already exists", errors.KeyUserErrorConflict, nil)
	}

	// Handle referral code
	var referee *model.User
	if referralCode != "" {
		referee, err = s.Repository.FindByReferral(ctx, referralCode)
		if err != nil {
			return err
		}

		// Update user with referral code and referring user ID
		user.UsedReferralCode = referralCode
		user.ReferringUserID = referee.ID
	}

	// Start user onboarding
	if err := s.onboarding(user); err != nil {
		return err
	}

	// Before creating the user, normalize the phone number to E.164 format.
	if user.Phone != "" {
		user.Phone, err = s.normalizePhoneNumber(user.Phone, "BR")
		if err != nil {
			return err
		}
	}

	userID, err := s.Repository.Create(ctx, user)
	if err != nil {
		return err
	}

	userObjectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return err
	}

	user.ID = userID
	user.ObjectID = userObjectID

	// Propagate error. It should be treaten in the Initialization of each service.
	if err := s.ProgressionService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.VaultService.Initialize(ctx, userID, referee != nil); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.DreamboardService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.FinancialDNAService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.FinancialSheetService.Initialize(ctx, userID, user.Name); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
	}

	// Update referee vault if referral code is used.
	if referralCode != "" && referee != nil {
		refereeVault, err := s.VaultService.FindByUser(ctx, referee.ID)
		if err != nil {
			return err
		}
		refereeVault.Coins = refereeVault.Coins + 10

		err = s.VaultService.Update(ctx, refereeVault)
		if err != nil {
			return err
		}
	}

	// Add user to Brevo list
	brevoList := os.Getenv("BREVO_USER_LIST_ID")
	if brevoList != "" {
		listID, err := strconv.ParseInt(brevoList, 10, 64)
		if err != nil {
			return err
		}
		if s.NotificationService.BrevoNotifier != nil {
			s.NotificationService.BrevoNotifier.AddUserToList(ctx, user, listID)
		}
	}

	return nil
}

func (s *service) CreateNewUser(ctx context.Context, user *model.User, referralCode string) error {
	foundUser, err := s.Repository.FindByEmail(ctx, user.Email)
	if err != nil {
		// If it's a "not found" error, continue with user creation
		if domainErr, ok := err.(*errors.DomainError); !ok || domainErr.Kind() != errors.NotFound {
			// Stop creation for any other type of error (internal errors, etc.), propagate it.
			return err
		}
	} else if foundUser != nil {
		return errors.NewConflictError(errors.Service, "user already exists", errors.KeyUserErrorConflict, nil)
	}

	// Handle referral code
	var referee *model.User
	if referralCode != "" {
		referee, err = s.Repository.FindByReferral(ctx, referralCode)
		if err != nil {
			return err
		}

		// Update user with referral code and referring user ID
		user.UsedReferralCode = referralCode
		user.ReferringUserID = referee.ID
	}

	// Before creating the user, normalize the phone number to E.164 format.
	if user.Phone != "" {
		user.Phone, err = s.normalizePhoneNumber(user.Phone, "BR")
		if err != nil {
			return err
		}
	}

	userID, err := s.Repository.Create(ctx, user)
	if err != nil {
		return err
	}

	userObjectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return err
	}

	user.ID = userID
	user.ObjectID = userObjectID

	// Propagate error. It should be treaten in the Initialization of each service.
	if err := s.ProgressionService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.VaultService.Initialize(ctx, userID, referee != nil); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.DreamboardService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.FinancialDNAService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.FinancialSheetService.Initialize(ctx, userID, user.Name); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
	}

	// Update referee vault if referral code is used.
	if referralCode != "" && referee != nil {
		refereeVault, err := s.VaultService.FindByUser(ctx, referee.ID)
		if err != nil {
			return err
		}
		refereeVault.Coins = refereeVault.Coins + 10

		err = s.VaultService.Update(ctx, refereeVault)
		if err != nil {
			return err
		}
	}

	// Add user to Brevo list
	brevoList := os.Getenv("BREVO_USER_LIST_ID")
	if brevoList != "" {
		listID, err := strconv.ParseInt(brevoList, 10, 64)
		if err != nil {
			return err
		}
		if s.NotificationService.BrevoNotifier != nil {
			s.NotificationService.BrevoNotifier.AddUserToList(ctx, user, listID)
		}
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*model.User, error) {
	return s.findUser(ctx, func(ctx context.Context) (*model.User, error) {
		userObjectID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return nil, errors.NewValidationError(errors.Service, "invalid user ID", errors.KeyUserErrorInvalidId, err)
		}
		return s.Repository.Find(ctx, userObjectID)
	})
}

func (s *service) FindAll(ctx context.Context) ([]*model.User, error) {
	users, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (s *service) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	user, err := s.Repository.FindByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindByPhone(ctx context.Context, phone string) (*model.User, error) {
	user, err := s.Repository.FindByPhone(ctx, phone)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindByReferral(ctx context.Context, referralCode string) (*model.User, error) {
	user, err := s.Repository.FindByReferral(ctx, referralCode)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindDeletedByEmail(ctx context.Context, email string) (*model.User, error) {
	user, err := s.Repository.FindDeletedByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// Update performs a full update with validation
func (s *service) Update(ctx context.Context, user *model.User) error {
	if err := user.Validate(); err != nil {
		return err
	}

	if user.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(user.ID)
		if err != nil {
			return errors.NewValidationError(errors.Service, "invalid user ID", errors.KeyUserErrorInvalidId, err)
		}
		user.ObjectID = objID
	}

	// Since user role manipulation is not allowed, we need to fetch the user from the database to get the current roles
	foundUser, err := s.Repository.Find(ctx, user.ObjectID)
	if err != nil {
		return err
	}
	user.Roles = foundUser.Roles

	// Normalize the phone number to E.164 format.
	if user.Phone != "" {
		normalizedPhone, err := s.normalizePhoneNumber(user.Phone, "BR")
		if err != nil {
			return err
		}
		user.Phone = normalizedPhone
	}

	return s.Repository.Update(ctx, user)
}

// Patch performs a partial update while preserving protected fields
func (s *service) Patch(ctx context.Context, user *model.User, patchData *model.User) error {
	// Keep existing email for Brevo update
	existingEmail := user.Email
	// Keep existing roles, referral code, used referral code, referring user ID, and register source since they cannot be modified via patch
	existingRoles := user.Roles
	existingReferralCode := user.ReferralCode
	existingUsedReferralCode := user.UsedReferralCode
	existingReferringUserID := user.ReferringUserID
	existingRegisterSource := user.RegisterSource

	// Prepare update with patch data
	if err := user.PrepareUpdate(patchData); err != nil {
		return err
	}

	// Restore roles to prevent modification
	user.Roles = existingRoles
	user.ReferralCode = existingReferralCode
	user.UsedReferralCode = existingUsedReferralCode
	user.ReferringUserID = existingReferringUserID
	user.RegisterSource = existingRegisterSource

	// Normalize the phone number to E.164 format.
	if user.Phone != "" {
		normalizedPhone, err := s.normalizePhoneNumber(user.Phone, "BR")
		if err != nil {
			return err
		}
		user.Phone = normalizedPhone
	}

	// Apply update without validation since it's a partial update
	if err := s.Repository.Update(ctx, user); err != nil {
		return err
	}

	// Update user in Brevo list
	if s.NotificationService.BrevoNotifier != nil {
		s.NotificationService.BrevoNotifier.UpdateUserInBrevo(ctx, existingEmail, user)
	}

	return nil
}

func (s *service) Delete(ctx context.Context, id string, reason *model.DeleteReason) error {
	foundUser, err := s.Find(ctx, id)
	if err != nil {
		return err
	}

	deletedUser := model.DeletedUser{
		User:         foundUser,
		DeleteReason: reason,
	}

	if err = s.Repository.CreateDelete(ctx, &deletedUser); err != nil {
		return err
	}

	err = s.Repository.Delete(ctx, foundUser.ObjectID)
	if err != nil {
		return err
	}
	return nil
}

// Card CRUD
func (s *service) FindCard(ctx context.Context, userId string) (*model.UserCard, error) {
	// Updated call to receive walletID string instead of wallet slice
	foundUser, foundAchievements, foundVault, currentSequence, err := s.getUserCardData(ctx, userId)
	if err != nil {
		return nil, err
	}

	userCard := &model.UserCard{
		ID:                foundUser.ID,
		Name:              foundUser.Name,
		Email:             foundUser.Email,
		PhotoURL:          foundUser.PhotoURL,
		Coins:             foundVault.Coins,
		AchievementsCount: int64(len(foundAchievements)),
		CurrentSequence:   currentSequence,
		Achievements:      foundAchievements,
		ReferralCode:      foundUser.ReferralCode,
		// Wallet and Investments will be defined later
	}

	return userCard, nil
}

// Utility
func (s *service) OnlyAdmin(ctx context.Context, id string) error {
	admins, err := s.Repository.FindAdmins(ctx)
	if err != nil {
		return err
	}

	if len(admins) <= 0 {
		return errors.NewNotFoundError(errors.Service, "admin users not found", errors.KeyUserErrorAdminUsersNotFound, nil)
	}

	onlyAdmin := true
	for _, admin := range admins {
		if admin.ID != id {
			onlyAdmin = false
		}
	}

	if onlyAdmin {
		return errors.NewNotFoundError(errors.Service, "admin users not found", errors.KeyUserErrorAdminUsersNotFound, nil)
	}

	return nil
}

func (s *service) UpdateFinancialProfile(ctx context.Context, userID string) error {
	// 1. Fetch the user model.
	user, err := s.Find(ctx, userID)
	if err != nil {
		return err
	}

	// 2. Fetch the challenge phase points
	points, err := s.ProgressionService.FindChallengePhaseCurrentPoints(ctx, userID, user.Classification, "67f6ddf3181babca8896e73c", "dna-financeiro", "perfil-financeiro")
	if err != nil {
		// Check if not found error
		if domainErr, ok := err.(*errors.DomainError); ok && domainErr.Kind() == errors.NotFound {
			points = -1 // Or any value that `classifyStatusFromPoints` knows means "Undefined"
			err = nil   // Clear the error so the function can proceed.
		} else {
			return err
		}
	}

	// 3. Determine the new status using the clean helper function.
	newStatus := s.classifyStatusFromPoints(points)

	// 4. Prepare the FinancialProfile struct for the update.
	now := time.Now()
	financialProfile := user.FinancialProfile
	isNewProfile := financialProfile == nil
	var oldStatus model.FinancialProfileStatus

	if financialProfile == nil {
		financialProfile = &model.FinancialProfile{
			CreatedAt: now,
		}
		oldStatus = model.StatusUndefined // Default for new profiles
	} else {
		oldStatus = financialProfile.Status
	}

	// Always update the status and timestamp.
	financialProfile.Status = newStatus
	financialProfile.UpdatedAt = now

	// 5. Create history record if status changed or if it's a new profile
	if isNewProfile || oldStatus != newStatus {
		history := &model.FinancialProfileHistory{
			UserID: userID,
			Status: newStatus,
		}
		if err := history.PrepareCreate(); err != nil {
			return err
		}
		if err := s.Repository.CreateFinancialProfileHistory(ctx, history); err != nil {
			return err
		}
	}

	// 6. Update the user's financial dna "me" with the new status
	tree, err := s.FinancialDNAService.FindByUser(ctx, userID)
	if err != nil {
		return err
	}

	// Find the "me" member and update their financial status
	me := tree.Members[0]
	if me != nil {
		me.FinancialStatus = s.convertToFinancialStatus(newStatus)
	}

	if err := s.FinancialDNAService.Update(ctx, tree); err != nil {
		return err
	}

	// 7. Save the changes.
	return s.Patch(ctx, user, &model.User{FinancialProfile: financialProfile})
}

// Helper

// classifyStatusFromPoints helper function remains the same.
func (s *service) classifyStatusFromPoints(points int) model.FinancialProfileStatus {
	if points < 0 { // Handles the "not completed" or "not found" case.
		return model.StatusUndefined
	}

	switch {
	case points <= 15:
		return model.StatusOverindebted
	case points <= 38:
		return model.StatusIndebted
	case points <= 61:
		return model.StatusBalanced
	default: // points >= 62
		return model.StatusInvestor
	}
}

// convertToFinancialStatus converts the financial profile status to the corresponding financial dna status
func (s *service) convertToFinancialStatus(status model.FinancialProfileStatus) financialdna.FinancialStatus {
	switch status {
	case model.StatusUndefined:
		return financialdna.FinancialStatusUndefined
	case model.StatusOverindebted:
		return financialdna.FinancialStatusOverindebted
	case model.StatusIndebted:
		return financialdna.FinancialStatusIndebted
	case model.StatusBalanced:
		return financialdna.FinancialStatusBalanced
	case model.StatusInvestor:
		return financialdna.FinancialStatusInvestor
	default:
		return financialdna.FinancialStatusUndefined
	}
}

// findUser is a helper function to find a user with error handling
func (s *service) findUser(ctx context.Context, findFn func(ctx context.Context) (*model.User, error)) (*model.User, error) {
	user, err := findFn(ctx)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// Updated return signature to return wallet ID string instead of wallet slice
func (s *service) getUserCardData(ctx context.Context, id string) (*model.User, []*string, *model.Vault, int, error) {
	foundUser, err := s.Find(ctx, id)
	if err != nil {
		return nil, nil, nil, 0, err
	}

	// Filter only conquered achievements
	foundAchievements, err := s.filterConqueredAchievements(ctx, foundUser.ID)
	if err != nil {
		return nil, nil, nil, 0, err
	}

	foundVault, err := s.VaultService.FindByUser(ctx, foundUser.ID)
	if err != nil {
		// Handle potential "not found" for vault gracefully if needed, otherwise propagate
		return nil, nil, nil, 0, err
	}

	foundFinancialSheet, err := s.FinancialSheetService.FindByUser(ctx, foundUser.ID)
	if err != nil {
		// Handle potential "not found" for financial sheet gracefully if needed, otherwise propagate
		return nil, nil, nil, 0, err
	}

	// Return wallet ID string
	return foundUser, foundAchievements, foundVault, foundFinancialSheet.Points.Current, nil
}

// filterConqueredAchievements filters only conquered achievements
func (s *service) filterConqueredAchievements(ctx context.Context, userID string) ([]*string, error) {
	// Get user achievements
	userAchievements, err := s.GamificationService.FindUserAchievements(ctx, userID)
	if err != nil {
		// Handle potential "not found" for user achievements gracefully if needed, otherwise propagate
		return nil, err
	}

	// Get content achievements
	contentAchievements, err := s.GamificationService.FindContentAchievements(ctx)
	if err != nil {
		// Handle potential "not found" for content achievements gracefully if needed, otherwise propagate
		return nil, err
	}

	// Return the slice of logo URLs for conquered achievements
	var conqueredAchievements []*string
	for _, userAchievement := range userAchievements {
		for _, contentAchievement := range contentAchievements {
			if userAchievement.Identifier == contentAchievement.Identifier {
				conqueredAchievements = append(conqueredAchievements, &contentAchievement.Logo)
				break
			}
		}
	}

	return conqueredAchievements, nil
}

func (s *service) onboarding(user *model.User) error {
	if err := user.Onboarding.AgeRange.IsValid(); err != nil {
		return err
	}
	user.Onboarding.AgeRange.AddLabel()

	if err := user.Onboarding.FinancialSituation.IsValid(); err != nil {
		return err
	}
	user.Onboarding.FinancialSituation.AddLabel()

	if err := user.Onboarding.FinancialGoal.IsValid(); err != nil {
		return err
	}
	user.Onboarding.FinancialGoal.AddLabel()

	for i, personalInterest := range user.Onboarding.PersonalInterests {
		if err := personalInterest.IsValid(); err != nil {
			return err
		}
		user.Onboarding.PersonalInterests[i].AddLabel()
	}

	user.Onboarding.CompletedAt = time.Now()

	return nil
}

// NormalizePhoneNumber normalizes the phone number to E.164 format.
func (s *service) normalizePhoneNumber(phone, regionCode string) (string, error) {
	// regionCode is the two-letter ISO country code (e.g., "US", "GB").
	// It's a hint for parsing numbers without a country code.
	num, err := phonenumbers.Parse(phone, regionCode)
	if err != nil {
		return "", errors.NewValidationError(errors.Model, "failed to parse phone number", errors.KeyUserErrorInvalidPhoneNumber, err)
	}

	if !phonenumbers.IsValidNumber(num) {
		return "", errors.NewValidationError(errors.Model, "invalid phone number", errors.KeyUserErrorInvalidPhoneNumber, nil)
	}

	// Format the number to the E.164 standard.
	e164Format := phonenumbers.Format(num, phonenumbers.E164)
	return e164Format, nil
}

// Role-based filtering methods

// FindAllWithRoleFilter returns all users that the requesting user can access based on their roles
func (s *service) FindAllWithRoleFilter(ctx context.Context, requestingUserID string, requestingUserRoles []string) ([]*model.User, error) {
	// Check permissions
	permissions := auth.GetPermissions(requestingUserRoles)

	// Admin can access all users
	if permissions.CanAccessAllData {
		return s.Repository.FindAll(ctx)
	}

	// Build filter based on user roles and permissions
	// filter := s.RBACService.BuildUserFilter(ctx, requestingUserID, requestingUserRoles)

	// Use repository method with filter (this would need to be implemented in the repository)
	// For now, we'll use the existing FindAll and filter in memory (not optimal for production)
	allUsers, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	var filteredUsers []*model.User
	for _, user := range allUsers {
		if s.RBACService.CanAccessResource(ctx, requestingUserID, requestingUserRoles, user.ID) {
			filteredUsers = append(filteredUsers, user)
		}
	}

	return filteredUsers, nil
}

// FindWithRoleFilter returns a specific user if the requesting user can access them
func (s *service) FindWithRoleFilter(ctx context.Context, targetUserID string, requestingUserID string, requestingUserRoles []string) (*model.User, error) {
	// Check if the requesting user can access the target user
	canAccess, err := s.CanAccessUser(ctx, requestingUserID, requestingUserRoles, targetUserID)
	if err != nil {
		return nil, err
	}

	if !canAccess {
		return nil, errors.NewForbiddenError(errors.Service, "access denied", errors.KeyUserErrorAccessDenied, nil)
	}

	// If access is allowed, return the user
	return s.Find(ctx, targetUserID)
}

// CanAccessUser checks if the requesting user can access the target user based on roles and permissions
func (s *service) CanAccessUser(ctx context.Context, requestingUserID string, requestingUserRoles []string, targetUserID string) (bool, error) {
	// Users can always access their own data
	if requestingUserID == targetUserID {
		return true, nil
	}

	// Use RBAC service to check access
	return s.RBACService.CanAccessResource(ctx, requestingUserID, requestingUserRoles, targetUserID), nil
}
