package notification

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/notification"
)

type Reader interface {
	FindByUserID(ctx context.Context, userID string) (*notification.UserActivityState, error)
	FindByUserIDs(ctx context.Context, userIDs []string) ([]*notification.UserActivityState, error)
	FindEligibleUsers(ctx context.Context, criteria map[string]interface{}) ([]*notification.UserActivityState, error)
}

type Writer interface {
	Create(ctx context.Context, state *notification.UserActivityState) error
	Update(ctx context.Context, state *notification.UserActivityState) error
	UpdateLastLogin(ctx context.Context, userID string) error
	UpdateLastTransactionLogged(ctx context.Context, userID string) error
	UpdateLastProgressionActivity(ctx context.Context, userID string) error
	UpdateNotificationSent(ctx context.Context, userID, notificationID string) error
	UpdateOnboardingEvent(ctx context.Context, userID, eventType string) error
	Upsert(ctx context.Context, state *notification.UserActivityState) error
	Delete(ctx context.Context, userID string) error
}

type Repository interface {
	Reader
	Writer
}
