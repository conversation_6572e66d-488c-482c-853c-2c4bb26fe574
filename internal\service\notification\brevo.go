package notification

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	brevo "github.com/getbrevo/brevo-go/lib"
)

// BrevoNotifier implements the Notifier interface using Brevo (formerly SendinBlue)
type BrevoNotifier struct {
	client *brevo.APIClient
	config *Config
}

// Brevo template IDs - these would need to be configured in Brevo dashboard
const (
	brevoPasswordResetTemplateID       = 1 // Replace with actual template ID
	brevoSubscriptionConfirmTemplateID = 2 // Replace with actual template ID
	brevoSubscriptionCancelTemplateID  = 3 // Replace with actual template ID
	brevoNewUserWelcomeTemplateID      = 4 // Replace with actual template ID
)

// NewBrevoNotifier creates a new Brevo notifier
func NewBrevoNotifier(config *Config) (*BrevoNotifier, error) {
	if config.BrevoAPIKey == "" {
		return nil, errors.New(errors.Service, "Brevo API key is required", errors.BadRequest, nil)
	}

	cfg := brevo.NewConfiguration()
	cfg.AddDefaultHeader("api-key", config.BrevoAPIKey)

	client := brevo.NewAPIClient(cfg)

	return &BrevoNotifier{
		client: client,
		config: config,
	}, nil
}

// SendPasswordReset sends a password reset email using Brevo
func (b *BrevoNotifier) SendPasswordReset(ctx context.Context, recipientEmail, recipientName, resetLink string) error {
	// Create the email request
	sendSmtpEmail := brevo.SendSmtpEmail{
		To: []brevo.SendSmtpEmailTo{
			{
				Email: recipientEmail,
				Name:  recipientName,
			},
		},
		Sender: &brevo.SendSmtpEmailSender{
			Email: b.config.BrevoSenderEmail,
			Name:  b.config.BrevoSenderName,
		},
		TemplateId: int64(brevoPasswordResetTemplateID),
		Params: map[string]interface{}{
			"resetLink":     resetLink,
			"recipientName": recipientName,
		},
	}

	// Send the email
	result, response, err := b.client.TransactionalEmailsApi.SendTransacEmail(ctx, sendSmtpEmail)
	if err != nil {
		return errors.New(errors.Service, "failed to send password reset email", errors.Internal, err)
	}

	if response.StatusCode >= 400 {
		return errors.New(errors.Service, errors.Message(fmt.Sprintf("Brevo returned error status: %d", response.StatusCode)), errors.Internal, nil)
	}

	// Log success (optional)
	_ = result // result contains message ID for tracking

	return nil
}

// SendSubscriptionConfirmation sends a subscription confirmation email
func (b *BrevoNotifier) SendSubscriptionConfirmation(ctx context.Context, recipientEmail, recipientName, planType string) error {
	sendSmtpEmail := brevo.SendSmtpEmail{
		To: []brevo.SendSmtpEmailTo{
			{
				Email: recipientEmail,
				Name:  recipientName,
			},
		},
		Sender: &brevo.SendSmtpEmailSender{
			Email: b.config.BrevoSenderEmail,
			Name:  b.config.BrevoSenderName,
		},
		TemplateId: int64(brevoSubscriptionConfirmTemplateID),
		Params: map[string]interface{}{
			"recipientName": recipientName,
			"planType":      planType,
		},
	}

	result, response, err := b.client.TransactionalEmailsApi.SendTransacEmail(ctx, sendSmtpEmail)
	if err != nil {
		return errors.New(errors.Service, "failed to send subscription confirmation email", errors.Internal, err)
	}

	if response.StatusCode >= 400 {
		return errors.New(errors.Service, errors.Message(fmt.Sprintf("Brevo returned error status: %d", response.StatusCode)), errors.Internal, nil)
	}

	_ = result
	return nil
}

// SendSubscriptionCancellation sends a subscription cancellation email
func (b *BrevoNotifier) SendSubscriptionCancellation(ctx context.Context, recipientEmail, recipientName, planType string) error {
	sendSmtpEmail := brevo.SendSmtpEmail{
		To: []brevo.SendSmtpEmailTo{
			{
				Email: recipientEmail,
				Name:  recipientName,
			},
		},
		Sender: &brevo.SendSmtpEmailSender{
			Email: b.config.BrevoSenderEmail,
			Name:  b.config.BrevoSenderName,
		},
		TemplateId: int64(brevoSubscriptionCancelTemplateID),
		Params: map[string]interface{}{
			"recipientName": recipientName,
			"planType":      planType,
		},
	}

	result, response, err := b.client.TransactionalEmailsApi.SendTransacEmail(ctx, sendSmtpEmail)
	if err != nil {
		return errors.New(errors.Service, "failed to send subscription cancellation email", errors.Internal, err)
	}

	if response.StatusCode >= 400 {
		return errors.New(errors.Service, errors.Message(fmt.Sprintf("Brevo returned error status: %d", response.StatusCode)), errors.Internal, nil)
	}

	_ = result
	return nil
}

// SendNewUserWelcome sends a welcome email to new users with password setup link
func (b *BrevoNotifier) SendNewUserWelcome(ctx context.Context, recipientEmail, recipientName, resetLink, planType string) error {
	sendSmtpEmail := brevo.SendSmtpEmail{
		To: []brevo.SendSmtpEmailTo{
			{
				Email: recipientEmail,
				Name:  recipientName,
			},
		},
		Sender: &brevo.SendSmtpEmailSender{
			Email: b.config.BrevoSenderEmail,
			Name:  b.config.BrevoSenderName,
		},
		TemplateId: int64(brevoNewUserWelcomeTemplateID),
		Params: map[string]interface{}{
			"recipientName": recipientName,
			"resetLink":     resetLink,
			"planType":      planType,
		},
	}

	result, response, err := b.client.TransactionalEmailsApi.SendTransacEmail(ctx, sendSmtpEmail)
	if err != nil {
		return errors.New(errors.Service, "failed to send new user welcome email", errors.Internal, err)
	}

	if response.StatusCode >= 400 {
		return errors.New(errors.Service, errors.Message(fmt.Sprintf("Brevo returned error status: %d", response.StatusCode)), errors.Internal, nil)
	}

	_ = result
	return nil
}

// SendGenericEmail sends a generic email with custom content
func (b *BrevoNotifier) SendGenericEmail(ctx context.Context, recipientEmail, recipientName, subject, content string) error {
	sendSmtpEmail := brevo.SendSmtpEmail{
		To: []brevo.SendSmtpEmailTo{
			{
				Email: recipientEmail,
				Name:  recipientName,
			},
		},
		Sender: &brevo.SendSmtpEmailSender{
			Email: b.config.BrevoSenderEmail,
			Name:  b.config.BrevoSenderName,
		},
		Subject:     subject,
		HtmlContent: content,
	}

	result, response, err := b.client.TransactionalEmailsApi.SendTransacEmail(ctx, sendSmtpEmail)
	if err != nil {
		return errors.New(errors.Service, "failed to send generic email", errors.Internal, err)
	}

	if response.StatusCode >= 400 {
		return errors.New(errors.Service, errors.Message(fmt.Sprintf("Brevo returned error status: %d", response.StatusCode)), errors.Internal, nil)
	}

	_ = result
	return nil
}

// AddUserToList adds a user to a Brevo list
func (b *BrevoNotifier) AddUserToList(ctx context.Context, user *model.User, listID int64) {
	// The validation error is a programming error, so we log it critically.
	if listID == 0 {
		log.Printf("CRITICAL: AddUserToList called with an invalid listID (0). UserID: %s", user.ID)
		return
	}

	// Convert onboarding PersonalInterests data to string
	personalInterests := []string{}
	for _, interest := range user.Onboarding.PersonalInterests {
		personalInterests = append(personalInterests, strconv.Itoa(int(interest.ID)))
	}
	personalInterestsStr := strings.Join(personalInterests, ",")

	// [FIX] Convert the user's IANA timezone to the Brevo-specific format.
	brevoTimezone, err := convertIANAToBrevoTimezone(user.Timezone)
	if err != nil {
		// Log a warning but continue, so a bad timezone doesn't stop the whole contact creation.
		log.Printf("WARNING: Could not convert timezone for UserID %s. Timezone: '%s', Error: %v", user.ID, user.Timezone, err)
	}

	// Check if financial profile is nil
	if user.FinancialProfile == nil {
		log.Printf("WARNING: Financial profile is nil for UserID %s. Setting financial profile to undefined.", user.ID)
		user.FinancialProfile = &model.FinancialProfile{Status: model.StatusUndefined}
	}

	// Prepare the contact attributes
	attributes := map[string]interface{}{
		"NOME":                  user.Name,
		"SOBRENOME":             user.LastName,
		"CONTACT_TIMEZONE":      brevoTimezone,
		"AGE_RANGE":             strconv.Itoa(int(user.Onboarding.AgeRange.ID)),           // Convert to string for Brevo
		"FINANCIAL_SITUATION":   strconv.Itoa(int(user.Onboarding.FinancialSituation.ID)), // Convert to string for Brevo
		"FINANCIAL_GOAL":        strconv.Itoa(int(user.Onboarding.FinancialGoal.ID)),      // Convert to string for Brevo
		"PERSONAL_INTERESTS":    personalInterestsStr,
		"FINANCIAL_PROFILE":     strconv.Itoa(int(user.FinancialProfile.Status)), // Convert to string for Brevo
		"DINBORA_PLUS":          false,
		"REGISTER_SOURCE":       user.RegisterSource,
		"REFERRAL_CODE":         user.ReferralCode,
		"USED_REFERRAL_CODE":    user.UsedReferralCode,
		"REFERRING_USER_ID":     user.ReferringUserID,
		"DINBORA_CREATION_DATE": user.CreatedAt,
	}

	// Only add SMS and WHATSAPP attributes if a phone number is provided
	if user.Phone != "" {
		// IMPORTANT: Ensure the phoneNumber is in E.164 format (e.g., +12125551234)
		// before it's passed to this function.
		attributes["SMS"] = user.Phone
		attributes["WHATSAPP"] = user.Phone
	}

	// Create the contact payload
	contact := brevo.CreateContact{
		ExtId:      user.ObjectID.Hex(),
		Email:      user.Email,
		Attributes: attributes, // <-- Use the new attributes map
		ListIds:    []int64{listID},
	}

	// Call the Brevo API
	_, resp, err := b.client.ContactsApi.CreateContact(ctx, contact)
	if err != nil {
		// Handle the duplicate contact case silently (or with a debug-level log).
		// This is expected behavior, not an error.
		if resp != nil && resp.StatusCode == 400 {
			log.Printf("DEBUG: Contact %s already exists in Brevo. UserID: %s", user.Email, user.ID)
			return
		}

		// This is a real, unexpected error. Log it with details.
		log.Printf("ERROR: Failed to add contact %s to Brevo list %d. UserID: %s, Error: %v", user.Email, listID, user.ID, err)
		return // Stop execution, but don't return an error.
	}

	log.Printf("INFO: Successfully added user %s to Brevo list %d. UserID: %s", user.Email, listID, user.ID)
}

// UpdateUserInBrevo updates a user in Brevo
func (b *BrevoNotifier) UpdateUserInBrevo(ctx context.Context, oldEmail string, user *model.User) {
	// [FIX] Convert the user's IANA timezone to the Brevo-specific format.
	brevoTimezone, err := convertIANAToBrevoTimezone(user.Timezone)
	if err != nil {
		// Log a warning but continue, so a bad timezone doesn't stop the whole contact update.
		log.Printf("WARNING: Could not convert timezone for UserID %s. Timezone: '%s', Error: %v", user.ID, user.Timezone, err)
	}

	// Check if financial profile is nil
	if user.FinancialProfile == nil {
		log.Printf("WARNING: Financial profile is nil for UserID %s. Setting financial profile to undefined.", user.ID)
		user.FinancialProfile = &model.FinancialProfile{Status: model.StatusUndefined}
	}

	// Prepare the contact attributes
	attributes := map[string]interface{}{
		"EMAIL":              user.Email,
		"NOME":               user.Name,
		"SOBRENOME":          user.LastName,
		"CONTACT_TIMEZONE":   brevoTimezone,
		"FINANCIAL_PROFILE":  strconv.Itoa(int(user.FinancialProfile.Status)), // Convert to string for Brevo
		"USED_REFERRAL_CODE": user.UsedReferralCode,
		"REFERRING_USER_ID":  user.ReferringUserID,
	}

	// Only add SMS and WHATSAPP attributes if a phone number is provided
	if user.Phone != "" {
		// IMPORTANT: Ensure the phoneNumber is in E.164 format (e.g., +12125551234)
		// before it's passed to this function.
		attributes["SMS"] = user.Phone
		attributes["WHATSAPP"] = user.Phone
	}

	// Create the contact payload
	contact := brevo.UpdateContact{
		Attributes: attributes, // <-- Use the new attributes map
	}

	// Call the Brevo API
	resp, err := b.client.ContactsApi.UpdateContact(ctx, oldEmail, contact)
	if err != nil {
		// There is no reason to check duplication since is an update operation.

		// This is a real, unexpected error. Log it with details.
		log.Printf("ERROR: Failed to update contact %s in Brevo. UserID: %s, Error: %v", oldEmail, user.ID, err)
		return // Stop execution, but don't return an error.
	}

	log.Printf("INFO: %s: Successfully updated user %s in Brevo. UserID: %s", resp.Status, user.Email, user.ID)
}

// UpdateUserAttributes updates a user's attributes in Brevo
func (b *BrevoNotifier) UpdateUserAttributes(ctx context.Context, userEmail string, attributes map[string]interface{}) error {
	// Create the contact payload
	contact := brevo.UpdateContact{
		Attributes: attributes, // <-- Use the new attributes map
	}

	// Call the Brevo API
	resp, err := b.client.ContactsApi.UpdateContact(ctx, userEmail, contact)
	if err != nil {
		// There is no reason to check duplication since is an update operation.

		// This is a real, unexpected error. Log it with details.
		log.Printf("ERROR: Failed to update contact %s in Brevo. Error: %v", userEmail, err)
		return err // Stop execution, but don't return an error.
	}

	log.Printf("INFO: %s: Successfully updated user %s in Brevo.", resp.Status, userEmail)
	return nil
}

// UpdateDinboraPlusStatus updates a user's Dinbora Plus status in Brevo
func (b *BrevoNotifier) UpdateDinboraPlusStatus(ctx context.Context, userEmail string, hasDinboraPlus bool) error {
	attributes := map[string]interface{}{
		"DINBORA_PLUS": hasDinboraPlus,
	}

	return b.UpdateUserAttributes(ctx, userEmail, attributes)
}

// Helper
// convertIANAToBrevoTimezone converts a standard IANA timezone (e.g., "America/Sao_Paulo")
// into the specific format required by Brevo (e.g., "(GMT-03:00) Sao Paulo").
func convertIANAToBrevoTimezone(ianaTimezone string) (string, error) {
	if ianaTimezone == "" {
		return "", nil // Return empty if no timezone is provided
	}

	// Load the location based on the IANA name
	location, err := time.LoadLocation(ianaTimezone)
	if err != nil {
		return "", fmt.Errorf("invalid IANA timezone '%s': %w", ianaTimezone, err)
	}

	// Get the current time in that location to determine the current offset
	t := time.Now().In(location)
	_, offsetSeconds := t.Zone() // Returns abbreviation (e.g., "BRT") and offset in seconds

	// Format the offset into ±HH:MM format
	offsetHours := offsetSeconds / 3600
	offsetMinutes := (offsetSeconds % 3600) / 60
	if offsetMinutes < 0 {
		offsetMinutes = -offsetMinutes
	}
	offsetString := fmt.Sprintf("%+03d:%02d", offsetHours, offsetMinutes) // %+03d ensures sign and padding

	// Extract the city/region name from the IANA string
	parts := strings.Split(ianaTimezone, "/")
	cityName := strings.ReplaceAll(parts[len(parts)-1], "_", " ") // Take last part and replace underscores

	// Combine into the final Brevo format
	brevoFormat := fmt.Sprintf("(GMT%s) %s", offsetString, cityName)

	return brevoFormat, nil
}
