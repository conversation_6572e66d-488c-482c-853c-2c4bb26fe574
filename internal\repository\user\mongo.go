package user

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/repository"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection                        *mongo.Collection
	trash                             *mongo.Collection
	financialProfileHistoryCollection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection:                        db.Collection(repository.USERS_COLLECTION),
		trash:                             db.Collection(repository.USERS_COLLECTION_TRASH),
		financialProfileHistoryCollection: db.Collection(repository.USERS_FINANCIAL_PROFILE_HISTORY),
	}

	// Create indexes
	ctx := context.Background()

	// Email index (unique) for main collection
	_, err := repo.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "email", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("userEmail"),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		db.Client().Disconnect(ctx)
	}

	// Email index for trash collection
	_, err = repo.trash.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "user.email", Value: 1}},
			Options: options.Index().SetName("userEmail"),
		},
	)
	if err != nil {
		db.Client().Disconnect(ctx)
	}

	// ReferralCode index
	_, err = repo.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys: bson.D{{Key: "referralCode", Value: 1}},
		},
	)
	if err != nil {
		db.Client().Disconnect(ctx)
	}

	// Financial Profile History index on userID and createdAt
	_, err = repo.financialProfileHistoryCollection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userID", Value: 1}, {Key: "createdAt", Value: 1}},
			Options: options.Index().SetName("userID_createdAt"),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		db.Client().Disconnect(ctx)
	}

	// Phone index
	_, err = repo.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "phone", Value: 1}},
			Options: options.Index().SetUnique(true).SetSparse(true),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on phone field: %v", err)
		db.Client().Disconnect(ctx)
	}

	return repo
}

func (m mongoDB) Create(ctx context.Context, user *model.User) (string, error) {
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()

	insertedResult, err := m.collection.InsertOne(ctx, user)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.NewConflictError(errors.Repository, "user already exists", errors.KeyUserErrorConflict, err)
		}

		return "", errors.NewInternalError(errors.Repository, "failed to create user", errors.KeyUserErrorCreateFailed, err)
	}

	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (m mongoDB) CreateDelete(ctx context.Context, deletedUser *model.DeletedUser) error {
	deletedUser.DeletedAt = time.Now()

	_, err := m.trash.InsertOne(ctx, deletedUser)
	if err != nil {
		// No need to check duplicate Key since the restore function is not implemented yet.
		// if mongo.IsDuplicateKeyError(err) {
		// 	return errors.New(errors.Repository, errors.DeletedUserConflictExists, errors.Conflict, err)
		// }

		return errors.NewInternalError(errors.Repository, "failed to create deleted user entry", errors.KeyUserErrorDeletedCreateFailed, err)
	}

	return nil
}

func (m mongoDB) Find(ctx context.Context, id primitive.ObjectID) (*model.User, error) {
	var user model.User
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: id}}).Decode(&user); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "user by ID not found", errors.KeyUserErrorNotFoundById, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "find user by ID failed", errors.KeyUserErrorFindByIdFailed, err)
	}

	user.ID = user.ObjectID.Hex()
	return &user, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*model.User, error) {
	cursor, err := m.collection.Find(ctx, bson.D{})
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "user not found", errors.KeyUserErrorFindAllFailed, err)
	}

	var users []*model.User
	for cursor.Next(ctx) {
		var user model.User
		if err = cursor.Decode(&user); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "decode user failed", errors.KeyUserErrorDecodeUserFailed, err)
		}
		user.ID = user.ObjectID.Hex()
		users = append(users, &user)
	}

	return users, nil
}

func (m mongoDB) FindAdmins(ctx context.Context) ([]*model.User, error) {
	cursor, err := m.collection.Find(ctx, bson.D{primitive.E{Key: "roles", Value: "admin"}})
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "admin users not found", errors.KeyUserErrorAdminUsersNotFound, err)
	}

	var users []*model.User
	for cursor.Next(ctx) {
		var user model.User
		if err = cursor.Decode(&user); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "decode admin user failed", errors.KeyUserErrorDecodeAdminUserFailed, err)
		}
		user.ID = user.ObjectID.Hex()
		users = append(users, &user)
	}

	return users, nil
}

func (m mongoDB) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "email", Value: email}}).Decode(&user); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "user email not found", errors.KeyUserErrorNotFoundByEmail, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "find user by email failed", errors.KeyUserErrorFindByEmailFailed, err)
	}

	user.ID = user.ObjectID.Hex()
	return &user, nil
}

// Now phone is a unique field, so we can use FindOne instead of Find
func (m mongoDB) FindByPhone(ctx context.Context, phone string) (*model.User, error) {
	var user model.User
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "phone", Value: phone}}).Decode(&user); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "user by phone not found", errors.KeyUserErrorNotFoundByPhone, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "find user by phone failed", errors.KeyUserErrorFindByPhoneFailed, err)
	}

	user.ID = user.ObjectID.Hex()
	return &user, nil
}

func (m mongoDB) FindByReferral(ctx context.Context, referralCode string) (*model.User, error) {
	var user model.User
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "referralCode", Value: referralCode}}).Decode(&user); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "user by referral code not found", errors.KeyUserErrorNotFoundByReferral, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "find user by referral code failed", errors.KeyUserErrorFindByReferralFailed, err)
	}

	user.ID = user.ObjectID.Hex()
	return &user, nil
}

func (m mongoDB) FindByReferringUserID(ctx context.Context, referringUserID string) ([]*model.User, error) {
	cursor, err := m.collection.Find(ctx, bson.D{primitive.E{Key: "referringUserId", Value: referringUserID}})
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "find users by referring user ID failed", errors.KeyUserErrorFindByReferringUserIdFailed, err)
	}
	defer cursor.Close(ctx)

	var users []*model.User
	for cursor.Next(ctx) {
		var user model.User
		if err = cursor.Decode(&user); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "decode user failed", errors.KeyUserErrorDecodeUserFailed, err)
		}
		user.ID = user.ObjectID.Hex()
		users = append(users, &user)
	}

	if err = cursor.Err(); err != nil {
		return nil, errors.NewInternalError(errors.Repository, "cursor error", errors.KeyUserErrorCursorError, err)
	}

	return users, nil
}

func (m mongoDB) FindWithFilter(ctx context.Context, filter interface{}) ([]*model.User, error) {
	cursor, err := m.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "find users with filter failed", errors.KeyUserErrorFindWithFilterFailed, err)
	}
	defer cursor.Close(ctx)

	var users []*model.User
	for cursor.Next(ctx) {
		var user model.User
		if err = cursor.Decode(&user); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "decode user failed", errors.KeyUserErrorDecodeUserFailed, err)
		}
		user.ID = user.ObjectID.Hex()
		users = append(users, &user)
	}

	if err = cursor.Err(); err != nil {
		return nil, errors.NewInternalError(errors.Repository, "cursor error", errors.KeyUserErrorCursorError, err)
	}

	return users, nil
}

// FindUsersWithFCMTokens finds users who have FCM tokens registered
func (m mongoDB) FindUsersWithFCMTokens(ctx context.Context) ([]*model.User, error) {
	// Join with fcm_tokens collection to find users with FCM tokens, handling the type mismatch
	pipeline := []bson.M{
		{
			"$lookup": bson.M{
				"from": repository.FCM_TOKENS_COLLECTION,
				// Use 'let' to define a variable for the user's _id
				"let": bson.M{"user_obj_id": "$_id"},
				// Use 'pipeline' to perform a more complex join
				"pipeline": []bson.M{
					{
						// In this sub-pipeline, we match on the converted types
						"$match": bson.M{
							"$expr": bson.M{
								// $eq compares two values.
								// We compare the 'userID' (string) from fcm_tokens...
								// ...with the user's _id that we converted to a string.
								"$eq": []interface{}{"$userID", bson.M{"$toString": "$$user_obj_id"}},
							},
						},
					},
					{
						// Limit to 1 because we only need to know if at least one exists
						"$limit": 1,
					},
				},
				"as": "fcm",
			},
		},
		{
			// This match condition remains the same and is still correct
			"$match": bson.M{
				"fcm": bson.M{"$ne": []interface{}{}},
			},
		},
		{
			"$project": bson.M{
				"fcm": 0, // Remove the joined field
			},
		},
	}

	cursor, err := m.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "failed to find users with FCM tokens", errors.KeyUserErrorFindWithFilterFailed, err)
	}
	defer cursor.Close(ctx)

	var users []*model.User
	// The decoding logic remains the same
	for cursor.Next(ctx) {
		var user model.User
		if err := cursor.Decode(&user); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "failed to decode user", errors.KeyUserErrorDecodeUserFailed, err)
		}
		user.ID = user.ObjectID.Hex()
		users = append(users, &user)
	}

	return users, nil
}

func (m mongoDB) FindDeletedByEmail(ctx context.Context, email string) (*model.User, error) {
	var deletedUser model.DeletedUser

	if err := m.trash.FindOne(ctx,
		bson.D{primitive.E{Key: "user.email", Value: email}}).Decode(&deletedUser); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "deleted user by email not found", errors.KeyUserErrorDeletedNotFoundByEmail, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "find deleted user by email failed", errors.KeyUserErrorDeletedFindByEmailFailed, err)
	}

	deletedUser.User.ID = deletedUser.User.ObjectID.Hex()
	return deletedUser.User, nil
}

func (m mongoDB) Update(ctx context.Context, user *model.User) error {
	if user.ObjectID.IsZero() {
		return errors.NewValidationError(errors.Repository, "invalid user ID", errors.KeyUserErrorInvalidId, nil)
	}

	user.UpdatedAt = time.Now()

	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: user.ObjectID}},
		primitive.M{"$set": user})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.NewConflictError(errors.Repository, "user update would cause conflict", errors.KeyUserErrorConflictUpdate, err)
		}

		return errors.NewInternalError(errors.Repository, "failed to update user", errors.KeyUserErrorUpdateFailed, err)
	}

	if result.MatchedCount <= 0 {
		return errors.NewNotFoundError(errors.Repository, "user not found for update", errors.KeyUserErrorNotFoundForUpdate, nil)
	}

	return nil
}

func (m mongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: id}})
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to delete user", errors.KeyUserErrorDeleteFailed, err)
	}

	if result.DeletedCount <= 0 {
		return errors.NewNotFoundError(errors.Repository, "user not found for deletion", errors.KeyUserErrorNotFoundForDeletion, nil)
	}

	return nil
}

// Financial Profile History operations

func (m mongoDB) CreateFinancialProfileHistory(ctx context.Context, history *model.FinancialProfileHistory) error {
	result, err := m.financialProfileHistoryCollection.InsertOne(ctx, history)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to create financial profile history", errors.KeyUserErrorCreateFailed, err)
	}

	history.ObjectID = result.InsertedID.(primitive.ObjectID)
	history.ID = history.ObjectID.Hex()
	return nil
}

func (m mongoDB) FindFinancialProfileHistory(ctx context.Context, userID string) ([]*model.FinancialProfileHistory, error) {
	filter := bson.M{"userID": userID}
	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: 1}}) // Sort by creation date ascending

	cursor, err := m.financialProfileHistoryCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "failed to find financial profile history", errors.KeyUserErrorFindWithFilterFailed, err)
	}
	defer cursor.Close(ctx)

	var histories []*model.FinancialProfileHistory
	if err = cursor.All(ctx, &histories); err != nil {
		return nil, errors.NewInternalError(errors.Repository, "failed to decode financial profile history", errors.KeyUserErrorDecodeUserFailed, err)
	}

	// Set ID field for each history record
	for _, history := range histories {
		history.ID = history.ObjectID.Hex()
	}

	return histories, nil
}

func (m mongoDB) FindFinancialProfileHistoryByUsers(ctx context.Context, userIDs []string) ([]*model.FinancialProfileHistory, error) {
	filter := bson.M{"userID": bson.M{"$in": userIDs}}
	opts := options.Find().SetSort(bson.D{{Key: "userID", Value: 1}, {Key: "createdAt", Value: 1}}) // Sort by user then creation date

	cursor, err := m.financialProfileHistoryCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "failed to find financial profile history by users", errors.KeyUserErrorFindWithFilterFailed, err)
	}
	defer cursor.Close(ctx)

	var histories []*model.FinancialProfileHistory
	if err = cursor.All(ctx, &histories); err != nil {
		return nil, errors.NewInternalError(errors.Repository, "failed to decode financial profile history", errors.KeyUserErrorDecodeUserFailed, err)
	}

	// Set ID field for each history record
	for _, history := range histories {
		history.ID = history.ObjectID.Hex()
	}

	return histories, nil
}
