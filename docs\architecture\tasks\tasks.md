This is an excellent question, and your project structure gives a very clear picture of your architecture. Based on your current layout, there's a very logical and clean place to put the `tasks` package and integrate it into your server's lifecycle.

### The Best Place for the `tasks` Package

The `tasks` package should be treated as a **top-level component of your application's core logic**, similar to `service`, `repository`, and `controller`. It orchestrates business logic (from services) on a schedule.

Therefore, the ideal location in your file tree is:

```
internal/
├───api
│   ├───controller
│   ├───middlewares
│   ├───repository
│   └───service (this seems to be a container/registry)
├───cache
├───controller (domain-specific controllers)
├───errors
├───migrator
├───model
├───repository (domain-specific repositories)
├───service (domain-specific services)
└───tasks/              <-- HERE
    ├───tasks.go
    └───brevo_sync.go
```

Place a new `tasks` directory directly under `internal/`. This keeps it at the same level of importance as your other core application layers.

### How to Initialize and Integrate It

You should initialize the scheduler and its tasks in your `Server.Start()` method, treating it as a **long-running background service** that needs to be started with the server and shut down gracefully. It's not a service in the sense of your `service.ServiceRegistry` (which seems to be for request-scoped logic), but rather a foundational process.

Here’s a step-by-step guide on how to integrate it into your existing `api/server.go`.

Of course. This is the perfect way to do it. Placing the method directly in the `subscriptionMongoDB` repository keeps your data access logic clean and centralized.

Based on your `Subscription` struct and repository code, here is the corrected and complete implementation.

---

#### Step 1: Update the `SubscriptionRepository` Interface

First, you need to add the new method signature to your `SubscriptionRepository` interface so that your services and tasks can use it.

```go
// in your internal/repository/billing/repository.go

type SubscriptionRepository interface {
	// ... existing methods like Find, FindByUser, etc. ...
	Create(ctx context.Context, subscription *billing.Subscription) error
	Update(ctx context.Context, subscription *billing.Subscription) error
	Delete(ctx context.Context, id primitive.ObjectID) error

	// Add the new method for the sync task
	FindUsersWithPotentiallyChangedSubscriptions(ctx context.Context, since time.Duration) ([]primitive.ObjectID, error)
}
```

#### Step 2: Add Necessary Indexes for Performance

Your new query will be checking `createdAt`, `updatedAt`, and `endDate`. To ensure this query is fast and doesn't require a full collection scan, you should add indexes for these fields.

Add the following to your `createIndexes` method in `subscription_mongodb.go`:

```go
// in internal/repository/billing/subscription_mongodb.go

func (r *subscriptionMongoDB) createIndexes() {
	ctx := context.Background()
	
	// ... your existing indexes for userId, userEmail, etc. ...

	// Index on endDate is already there, which is great.

	// NEW: Index on createdAt for finding new subscriptions
	_, err := r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "createdAt", Value: 1}},
			Options: options.Index().SetName("subscriptionCreatedAt"),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on subscriptions.createdAt field: %v", err)
	}

	// NEW: Index on updatedAt for finding recently updated subscriptions
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "updatedAt", Value: 1}},
			Options: options.Index().SetName("subscriptionUpdatedAt"),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on subscriptions.updatedAt field: %v", err)
	}
}
```

#### Step 3: Implement the `FindUsersWithPotentiallyChangedSubscriptions` Method

Now, add the new method to your `subscriptionMongoDB` struct. It will use the exact field names from your `Subscription` model (`userId`, `createdAt`, `updatedAt`, `endDate`).

```go
// in internal/repository/billing/subscription_mongodb.go, add this new method

// FindUsersWithPotentiallyChangedSubscriptions finds unique user IDs whose subscription
// status might have changed within the given duration. This is optimized for sync tasks.
func (r *subscriptionMongoDB) FindUsersWithPotentiallyChangedSubscriptions(ctx context.Context, since time.Duration) ([]primitive.ObjectID, error) {
	// Calculate the cutoff time for the query
	sinceTime := time.Now().UTC().Add(-since)

	// The query finds subscriptions that were:
	// 1. Created recently (new subscribers).
	// 2. Updated recently (cancellations, reactivations).
	// 3. Expired recently (endDate has passed but was within our time window).
	query := bson.M{
		"$or": []bson.M{
			{"createdAt": bson.M{"$gte": sinceTime}},
			{"updatedAt": bson.M{"$gte": sinceTime}},
			{
				"endDate": bson.M{
					"$gte": sinceTime,
					"$lt":  time.Now().UTC(),
				},
			},
		},
	}

	// We only need the userId field, so we can use a projection for efficiency.
	opts := options.Find().SetProjection(bson.M{"userId": 1})

	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query for changed subscriptions", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	// Use a map to efficiently collect unique user IDs.
	// A user could have multiple subscriptions that match (e.g., one expired, one created).
	uniqueUserIDs := make(map[primitive.ObjectID]bool)
	for cursor.Next(ctx) {
		var result struct {
			UserID primitive.ObjectID `bson:"userId"`
		}
		if err := cursor.Decode(&result); err != nil {
			// Log this error but continue, so one bad document doesn't stop the whole job.
			log.Printf("warning: failed to decode subscription for sync task: %v", err)
			continue
		}
		// A check to ensure we don't add an empty ObjectID
		if !result.UserID.IsZero() {
			uniqueUserIDs[result.UserID] = true
		}
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding changed subscriptions", errors.Internal, err)
	}

	// Convert the map of unique IDs into a slice.
	userIDs := make([]primitive.ObjectID, 0, len(uniqueUserIDs))
	for id := range uniqueUserIDs {
		userIDs = append(userIDs, id)
	}

	return userIDs, nil
}
```

#### Step 4: Update the `SubscriptionService` Interface

```go
// in your internal/service/billing/service.go

type SubscriptionService interface {
	// ... existing methods like Find, FindByUser, etc. ...
	Create(ctx context.Context, subscription *billing.Subscription) error
	Update(ctx context.Context, subscription *billing.Subscription) error
	Delete(ctx context.Context, id primitive.ObjectID) error

	// Add the new method for the sync task
	FindUsersWithPotentiallyChangedSubscriptions(ctx context.Context, since time.Duration) ([]primitive.ObjectID, error)
}
```

#### Step 5: Implement the Method in `SubscriptionService`

```go
// in your internal/service/billing/subscription.go

func (s *service) FindUsersWithPotentiallyChangedSubscriptions(ctx context.Context, since time.Duration) ([]primitive.ObjectID, error) {
	return s.repo.Subscriptions().FindUsersWithPotentiallyChangedSubscriptions(ctx, since)
}
```

### Final Integration

Now your `brevo_sync.go` task can be simplified. You no longer need a separate repository; you can just inject the standard `SubscriptionService`.

#### Step 6: Create the `internal/tasks` Package

Create the files as we discussed previously.

**`internal/tasks/tasks.go`** (The Scheduler)

```go
package tasks

// Package installation according to https://github.com/robfig/cron?tab=readme-ov-file
// go get github.com/robfig/cron/v3@v3.0.0

// For documentation and usage examples, see:
// https://pkg.go.dev/github.com/robfig/cron

import (
	"log"
	"time"

	"github.com/robfig/cron/v3"
)

// Task defines the interface that all scheduled tasks must implement.
type Task interface {
	Run()
}

// Scheduler encapsulates the cron engine.
type Scheduler struct {
	engine *cron.Cron
}

// NewScheduler creates and configures a new scheduler instance.
func NewScheduler() (*Scheduler, error) {
	// Best Practice: Always use a specific time zone, like UTC. Here we will use server timezone Sao Paulo.
	loc, err := time.LoadLocation("America/Sao_Paulo")
	if err != nil {
		return nil, err
	}

	engine := cron.New(cron.WithLocation(loc))

	return &Scheduler{
		engine: engine,
	}, nil
}

// AddTask schedules a new task.
func (s *Scheduler) AddTask(spec string, task Task) {
	_, err := s.engine.AddJob(spec, task)
	if err != nil {
		// Log a fatal error because a misconfigured cron spec is a developer error
		// that should be fixed immediately.
		log.Fatalf("Could not add task with spec '%s': %v", spec, err)
	}
}

// Start begins the scheduler's ticking.
func (s *Scheduler) Start() {
	log.Println("Starting task scheduler...")
	s.engine.Start()
}

// Stop gracefully stops the scheduler, waiting for running jobs to finish.
func (s *Scheduler) Stop() {
	log.Println("Stopping task scheduler...")
	<-s.engine.Stop().Done()
	log.Println("Task scheduler gracefully stopped.")
}
```

**`internal/tasks/brevo_sync.go`** (The Specific Task)

*This file would be exactly as we defined in the previous answer, containing `NewBrevoSyncTask`, the interfaces it needs, and its `Run()` method.*
```go
package tasks

import (
	"context"
	"errors"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	
	"github.com/dsoplabs/dinbora-backend/internal/model"
)

// --- INTERFACES ---
// These interfaces define the precise methods our task needs from your services.

// UserFinder defines the method needed to get user details.
type UserFinder interface {
	Find(ctx context.Context, id string) (*model.User, error)
}

// SubscriptionChecker defines methods to check a user's subscription status and find candidates for syncing.
type SubscriptionChecker interface {
	// HasDinboraPlusFeature is the key method to determine the user's current active state.
	HasDinboraPlusFeature(ctx context.Context, userID primitive.ObjectID) (bool, error)
	// FindUsersWithPotentiallyChangedSubscriptions finds users whose subscriptions have recently changed.
	FindUsersWithPotentiallyChangedSubscriptions(ctx context.Context, since time.Duration) ([]primitive.ObjectID, error)
}

// BrevoAttributeUpdater defines the method for updating a contact in Brevo.
type BrevoAttributeUpdater interface {
	// UpdateDinboraPlusStatus is the method from your notification service to update a contact.
	UpdateDinboraPlusStatus(ctx context.Context, userEmail string, hasDinboraPlus bool) error
}


// --- TASK IMPLEMENTATION ---

// BrevoSyncTask holds the dependencies for the Brevo sync job.
type BrevoSyncTask struct {
	userSvc         UserFinder
	subscriptionSvc SubscriptionChecker
	notificationSvc BrevoAttributeUpdater
	brevoListID     int64
}

// NewBrevoSyncTask is the updated constructor.
// It no longer requires a separate repository.
func NewBrevoSyncTask(
	userSvc UserFinder,
	subscriptionSvc SubscriptionChecker,
	notificationSvc BrevoAttributeUpdater,
	brevoListID int64,
) *BrevoSyncTask {
	return &BrevoSyncTask{
		userSvc:         userSvc,
		subscriptionSvc: subscriptionSvc,
		notificationSvc: notificationSvc,
		brevoListID:     brevoListID,
	}
}

// Run contains the core logic for the task.
func (t *BrevoSyncTask) Run() {
	ctx := context.Background()
	log.Println("Starting Brevo subscription attribute sync...")

	// Find user IDs whose subscriptions might have changed state recently.
	const syncInterval = time.Hour
	// This now correctly calls the method on the subscription service.
	userIDs, err := t.subscriptionSvc.FindUsersWithPotentiallyChangedSubscriptions(ctx, syncInterval)
	if err != nil {
		log.Printf("ERROR: Failed to find users for Brevo sync: %v", err)
		return
	}

	if len(userIDs) == 0 {
		log.Println("No users with state changes found for Brevo sync.")
		return
	}

	log.Printf("Found %d users to check for Brevo sync.", len(userIDs))

	for _, userID := range userIDs {
		// Process each user individually so one failure doesn't stop others.
		if err := t.syncUser(ctx, userID.Hex()); err != nil { // Convert ObjectID to string for user service
			log.Printf("ERROR: Failed to sync user %s to Brevo: %v", userID.Hex(), err)
		}
	}

	log.Println("Brevo subscription attribute sync finished.")
}

// syncUser encapsulates the logic for a single user. (This method remains the same)
func (t *BrevoSyncTask) syncUser(ctx context.Context, userID string) error {
	user, err := t.userSvc.Find(ctx, userID)
	if err != nil {
		// If user not found, we can't do anything.
		return err
	}
	if user.Email == "" {
		return errors.New("user has no email, cannot sync to Brevo")
	}

	// Convert string ID to ObjectID for the subscription service
	userObjectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return err
	}

	// Use the provided service method to get the true current state.
	isNowActive, err := t.subscriptionSvc.HasDinboraPlusFeature(ctx, userObjectID)
	if err != nil {
		log.Printf("Warning: could not check subscription status for user %s: %v. Syncing as 'false'.", userID, err)
		isNowActive = false // Default to false on error to be safe.
	}

	log.Printf("Syncing user %s (%s). Has active subscription: %t", userID, user.Email, isNowActive)
	
    // Call the Brevo-specific method to update the user's attributes.
    err = t.notificationSvc.UpdateDinboraPlusStatus(ctx, user.Email, isNowActive)
	if err != nil {
		return err
	}

	return nil
}
```

#### Step 7: Modify `api/server.go` to Manage the Scheduler

You will initialize, start, and stop the scheduler within the `Start()` method of your `Server`.

```go
// in api/server.go

// ... other imports
import (
    // ...
    "github.com/dsoplabs/dinbora-backend/internal/tasks" // <-- Import your new package
)


// Update the Server struct to hold the scheduler
type Server struct {
	ctx       context.Context
	echo      *echo.Echo
	db        *mongo.Database
	firebase  *firebase.App
	scheduler *tasks.Scheduler // <-- ADD THIS
}

// Update the New() function to initialize the scheduler
func New() (*Server, error) {
    // ... existing New() code ...

    // ... after db and firebaseApp are successfully initialized ...

    // Initialize the scheduler
    scheduler, err := tasks.NewScheduler()
    if err != nil {
        return nil, fmt.Errorf("scheduler initialization failed: %w", err)
    }

    // Create and return server instance
    return &Server{
        ctx:       ctx,
        db:        db,
        echo:      echo.New(),
        firebase:  firebaseApp,
        scheduler: scheduler, // <-- ADD THIS
    }, nil
}

// Update the Start() method to manage the scheduler's lifecycle
func (s *Server) Start() error {
    // ... existing setup code for http.Server, i18n, middlewares ...

    // === START OF NEW CODE ===

    // After initializing your service container, you have the dependencies for your tasks.
    // This part should come after you've initialized your services in the Router() method,
    // so let's move this logic there for better dependency management.

    // === END OF NEW CODE ===


    // ... the rest of the Start() method, including the graceful shutdown logic ...

	// --- MODIFY GRACEFUL SHUTDOWN ---
	// Before returning, add the scheduler shutdown.
	
    // Graceful shutdown for Echo server
    // ... existing echo shutdown code ...

    // Graceful shutdown for Task Scheduler
    if s.scheduler != nil {
        s.scheduler.Stop() // This is a blocking call.
    }

	// Graceful shutdown for MongoDB
	// ... existing mongo shutdown code ...

	return finalErr
}


// Router is the perfect place to register the tasks because it's where
// you initialize the service container which provides the dependencies for the tasks.
func (s *Server) Router() error {
    // ... existing code to initialize repoContainer, serviceContainer, controllerContainer ...
    service := serviceContainer.Initialize()
    controller := controllerContainer.Initialize()

    // === NEW LOGIC: INITIALIZE AND REGISTER TASKS ===
    // ===  We MUST add this to a function func initializeTasks(service *service.ServiceRegistry, scheduler *tasks.Scheduler) error { ... } and call it in Router() ===
    // Now that you have your services, you can create your tasks.
    if os.Getenv("BREVO_USER_LIST_ID") != "" {
        brevoUserListID, err := strconv.ParseInt(os.Getenv("BREVO_USER_LIST_ID"), 10, 64)
        if err != nil {
            return err
        }
    } else {
        brevoUserListID = 0
    }

    brevoSyncTask := tasks.NewBrevoSyncTask(
        service.User,          // Assuming service.User implements UserFinder
        service.Subscription,  // Assuming a new service.Subscription implements SubscriptionStateChecker
        service.Notification,  // Assuming service.Notification implements BrevoAttributeUpdater
        brevoUserListID,          // This should be a constant or a value from your config
    )

    // Register the task with its schedule.
    s.scheduler.AddTask("@hourly", brevoSyncTask)
    log.Println("Scheduled Brevo Sync Task to run hourly.")

    // You can add more tasks here
    // halfHourlyReportTask := tasks.NewHalfHourlyReportTask(service.Reporting)
    // This runs every hour on the half hour
    // s.scheduler.AddTask("0 30 * * * *", halfHourlyReportTask)
    // hourlyReportTask := tasks.NewHourlyReportTask(service.Reporting)
    // This runs every hour
    // s.scheduler.AddTask("0 0 * * * *", hourlyReportTask)
    // hourlyThirdReportTask := tasks.NewHourlyThirdReportTask(service.Reporting)
    // This runs every hour thirty
    // s.scheduler.AddTask("@every 1h30m", hourlyThirdReportTask)
    

    // Start the scheduler AFTER all tasks have been added.
    s.scheduler.Start()

    // === END OF NEW LOGIC ===


    // ... existing code for injecting middleware and running migrations ...
    
    // ...
	return nil
}
```

### Summary of Changes and Rationale

1.  **Package Location (`internal/tasks`)**: This places it correctly as a core component of your application logic.
2.  **Scheduler in `Server` Struct**: The `Server` struct is the owner of all long-running processes, so the scheduler belongs there.
3.  **Initialization in `New()`**: The scheduler itself is created along with the other core components like the DB connection.
4.  **Task Registration in `Router()`**: This is the key decision. You register the tasks inside `Router()` because this is where you have access to the fully initialized `service` container. Tasks are dependent on services for their logic, so you must wait until the services are ready before you can create the tasks.
5.  **Starting in `Router()`**: Starting the scheduler right after registering the tasks ensures it's ready to go as soon as the server is fully configured.
6.  **Graceful Shutdown in `Start()`**: You add `s.scheduler.Stop()` to your shutdown sequence. This ensures that if a job is running when you shut down the server, it's allowed to finish cleanly before the application exits.

This approach is clean, maintains separation of concerns, and correctly manages the lifecycle of your background tasks alongside your web server.